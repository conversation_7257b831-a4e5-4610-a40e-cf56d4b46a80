import { useState, useCallback } from 'react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { FashionLabImageService } from '../services/fashionLabImageService';
import { toast } from 'react-hot-toast';
import type { QueueProgress } from '../components/image-generator/types';

interface UseGenerateImagesOptions {
  collectionId: string;
  onSuccess?: (queueId: string) => void;
  onComplete?: (images: string[]) => void;
}

interface GenerateParams {
  prompt: string;
  faceImage: string;
  image2: string;
  image3: string;
  image4: string;
  metadata?: Record<string, unknown>;
  // Seed values for reproducible generation (optional)
  seed1?: number | null;
  seed2?: number | null;
  seed3?: number | null;
  seed4?: number | null;
  // Number of images to generate (will make multiple API calls)
  numImages?: number;
  // Delay between API calls in milliseconds
  apiDelay?: number;
}

export function useFashionLabImages(options: UseGenerateImagesOptions) {
  const queryClient = useQueryClient();
  const [queueProgress, setQueueProgress] = useState<QueueProgress[]>([]);
  const [overallProgress, setOverallProgress] = useState(0);
  const [currentPrompt, setCurrentPrompt] = useState<string>('');
  const [currentMetadata, setCurrentMetadata] = useState<Record<string, unknown> | null>(null);

  // Generate images mutation (V2 API)
  const generateMutation = useMutation({
    mutationFn: async (params: GenerateParams) => {
      setCurrentPrompt(params.prompt);
      setCurrentMetadata(params.metadata || null);

      const numImages = params.numImages || 1;
      const apiDelay = params.apiDelay ?? 500; // Use provided delay or default to 500ms
      const queueIds: string[] = [];
      const errors: string[] = [];

      console.log(`[FashionLab] Starting generation of ${numImages} images with ${apiDelay}ms delay`);

      // Initialize progress tracking for all images
      const initialProgress: QueueProgress[] = Array.from({ length: numImages }, (_, i) => ({
        queueId: '', // Will be set when we get the actual queue ID
        imageIndex: i + 1,
        status: 'pending',
        progress: 0,
      }));
      setQueueProgress(initialProgress);
      setOverallProgress(0);

      // Make multiple API calls for multiple images
      for (let i = 0; i < numImages; i++) {
        try {
          console.log(`[FashionLab] Generating image ${i + 1}/${numImages}`);

          // Update status to generating
          setQueueProgress(prev => prev.map((item, index) =>
            index === i ? { ...item, status: 'generating' as const } : item
          ));

          // Add configurable delay between API calls
          if (i > 0 && apiDelay > 0) {
            await new Promise(resolve => setTimeout(resolve, apiDelay));
          }
          
          const result = await FashionLabImageService.generateImages({
            prompt: params.prompt,
            faceImage: params.faceImage,
            image2: params.image2,
            image3: params.image3,
            image4: params.image4,
            collectionId: options.collectionId,
            storeOnCompletion: true,
            metadata: {
              ...params.metadata,
              imageIndex: i + 1,
              totalImages: numImages,
            },
            // Only include seeds if they are explicitly set (not null or undefined)
            // This allows the API to generate random seeds when not provided
            ...(params.seed1 !== null && params.seed1 !== undefined && { seed1: params.seed1 }),
            ...(params.seed2 !== null && params.seed2 !== undefined && { seed2: params.seed2 }),
            ...(params.seed3 !== null && params.seed3 !== undefined && { seed3: params.seed3 }),
            ...(params.seed4 !== null && params.seed4 !== undefined && { seed4: params.seed4 }),
            // Always pass numImages as 1 since we're making individual calls
            numImages: 1,
          });

          if (result && result.queue_id) {
            queueIds.push(result.queue_id);
            console.log(`[FashionLab] Successfully queued image ${i + 1}/${numImages} with queue_id: ${result.queue_id}`);

            // Update progress with queue ID
            setQueueProgress(prev => prev.map((item, index) =>
              index === i ? { ...item, queueId: result.queue_id, status: 'generating' as const } : item
            ));
          } else {
            console.error(`[FashionLab] Failed to get queue_id for image ${i + 1}/${numImages}`);
            errors.push(`Image ${i + 1}: No queue_id received`);

            // Mark as failed
            setQueueProgress(prev => prev.map((item, index) =>
              index === i ? { ...item, status: 'failed' as const, error: 'No queue_id received' } : item
            ));
          }
        } catch (error) {
          console.error(`[FashionLab] Error generating image ${i + 1}/${numImages}:`, error);
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          errors.push(`Image ${i + 1}: ${errorMessage}`);

          // Mark as failed
          setQueueProgress(prev => prev.map((item, index) =>
            index === i ? { ...item, status: 'failed' as const, error: errorMessage } : item
          ));

          // Continue with remaining images instead of failing completely
          continue;
        }
      }

      // If we got some queue IDs but not all, still return what we have
      if (queueIds.length > 0) {
        if (errors.length > 0) {
          console.warn(`[FashionLab] Partial success: ${queueIds.length}/${numImages} images queued. Errors:`, errors);
          toast.error(`Generated ${queueIds.length} of ${numImages} images. Some failed.`);
        }
        return queueIds;
      }

      // If no queue IDs were generated, throw an error
      if (queueIds.length === 0) {
        throw new Error(`Failed to generate any images. Errors: ${errors.join('; ')}`);
      }

      return queueIds;
    },
    onSuccess: (queueIds) => {
      setOverallProgress(0);
      toast.success(`Image generation started (${queueIds.length} ${queueIds.length === 1 ? 'image' : 'images'})`);

      // For backward compatibility, call onSuccess with the first queue ID
      if (queueIds.length > 0) {
        options.onSuccess?.(queueIds[0]);
      }

      // Start polling for completion of all queue IDs
      queueIds.forEach(queueId => pollForCompletion(queueId));
    },
    onError: (error: Error) => {
      toast.error(`Failed to generate images: ${error.message}`);
    },
  });

  // Poll for completion
  const pollForCompletion = useCallback(async (queueId: string) => {
    try {
      const result = await FashionLabImageService.waitForCompletion(
        queueId,
        options.collectionId,
        {
          onProgress: (progress) => {
            // Update progress for this specific queue
            setQueueProgress(prev => {
              const updated = prev.map(item =>
                item.queueId === queueId
                  ? { ...item, progress, status: 'generating' as const }
                  : item
              );

              // Calculate overall progress
              const totalProgress = updated.reduce((sum, item) => sum + item.progress, 0);
              const avgProgress = updated.length > 0 ? totalProgress / updated.length : 0;
              setOverallProgress(avgProgress);

              return updated;
            });
          },
          prompt: currentPrompt,
          metadata: currentMetadata || undefined,
        }
      );

      if (result.status === 'completed' && result.stored) {
        // Mark this queue as completed
        setQueueProgress(prev => {
          const updated = prev.map(item =>
            item.queueId === queueId
              ? { ...item, progress: 100, status: 'completed' as const }
              : item
          );

          // Check if all are completed
          const allCompleted = updated.every(item => item.status === 'completed' || item.status === 'failed');
          const completedCount = updated.filter(item => item.status === 'completed').length;

          if (allCompleted) {
            toast.success(`All images completed! ${completedCount} of ${updated.length} successful.`);

            // Invalidate queries to refresh image lists
            queryClient.invalidateQueries({ queryKey: ['ai-generated-images', options.collectionId] });
            queryClient.invalidateQueries({ queryKey: ['generated-images', options.collectionId] });

            setOverallProgress(0);
            // Clear progress after a delay to allow users to see completion
            setTimeout(() => setQueueProgress([]), 2000);
          }

          return updated;
        });

        if (result.images) {
          options.onComplete?.(result.images);
        }
      } else if (result.status === 'failed') {
        // Mark this queue as failed
        setQueueProgress(prev => prev.map(item =>
          item.queueId === queueId
            ? { ...item, status: 'failed' as const, error: 'Generation failed' }
            : item
        ));
        toast.error(`Image generation failed for queue ${queueId}`);
      }
    } catch (error) {
      // Mark this queue as failed
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      setQueueProgress(prev => prev.map(item =>
        item.queueId === queueId
          ? { ...item, status: 'failed' as const, error: errorMessage }
          : item
      ));
      toast.error(`Failed to complete image generation for queue ${queueId}`);
    }
  }, [options, queryClient, currentPrompt, currentMetadata]);

  // Query for generated images
  const generatedImagesQuery = useQuery({
    queryKey: ['generated-images', options.collectionId],
    queryFn: () => FashionLabImageService.getGeneratedImages(options.collectionId),
    enabled: !!options.collectionId,
  });

  // Check status manually
  const checkStatus = useCallback(async (queueId: string) => {
    const result = await FashionLabImageService.checkQueueStatus(
      queueId,
      options.collectionId
    );
    return result;
  }, [options.collectionId]);

  return {
    generate: generateMutation.mutate,
    isGenerating: generateMutation.isPending || queueProgress.some(q => q.status === 'generating' || q.status === 'pending'),
    progress: overallProgress,
    queueProgress, // New: individual progress tracking
    // For backward compatibility
    activeQueueIds: queueProgress.map(q => q.queueId).filter(Boolean),
    activeQueueId: queueProgress.find(q => q.queueId)?.queueId || null,
    generatedImages: generatedImagesQuery.data || [],
    isLoadingImages: generatedImagesQuery.isLoading,
    checkStatus,
    refetchImages: generatedImagesQuery.refetch,
  };
}