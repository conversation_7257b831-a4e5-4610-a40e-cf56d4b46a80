// Image Generation Configuration
// Centralized configuration for models, angles, and settings

export const CAMPAIGN_MODELS = [
  {
    id: 'S',
    name: 'Model S - Scandinavian',
    shortName: 'Model S',
    description: 'Blond scandinavian young woman in her twenties, thin and petite, not too tall',
    promptText: 'this scandinavian fashion model with blonde hair, young woman in her twenties, thin and petite build',
    lora: 'bubbleroom_model_s_v1',
    image: '/Library Models/Small/S_half body_3_4angle_left.jpg',
  },
  {
    id: 'M',
    name: 'Model M - European',
    shortName: 'Model M',
    description: 'European looking white woman with mid length dark brown hair and brown eyes, casual and confident',
    promptText: 'this european fashion model with mid-length dark brown hair, brown eyes, casual and confident demeanor',
    lora: 'bubbleroom_model_m_v1',
    image: '/Library Models/m_half_34_left.jpg',
  },
  {
    id: 'L',
    name: 'Model L - African American',
    shortName: 'Model L',
    description: 'African American woman in her thirties, wide hips and tall, long black hair, cheerful and casual',
    promptText: 'this african american fashion model, woman in her thirties, tall with wide hips, long black hair, cheerful expression',
    lora: 'bubbleroom_model_l_v1',
    image: '/Library Models/l_half_34_left.jpg',
  },
  {
    id: 'XL',
    name: 'Model XL - Latino',
    shortName: 'Model XL',
    description: 'Latino woman in her forties, wide hips and sand watch figure, feminine attitude, long black hair',
    promptText: 'this latino fashion model, woman in her forties, hourglass figure with wide hips, long black hair, feminine and graceful',
    lora: 'bubbleroom_model_xl_v1',
    image: '/Library Models/xl_half_34_left.jpg',
  }
] as const;

export const ANGLE_BLOCKS = [
  { id: 1, name: 'Full body face on', promptText: 'face on full height shot' },
  { id: 2, name: 'Full body backside', promptText: 'full body shot from behind' },
  { id: 3, name: 'Half body front', promptText: '3/4 full height angle' },
  { id: 4, name: 'Half body backside', promptText: 'half body shot from behind' }
] as const;

export const SETTING_BLOCKS = [
  {
    id: 'background',
    name: 'Background',
    icon: '🏞️',
    options: [
      { id: 1, name: 'White Seamless', promptText: 'on a white studio background' },
      { id: 2, name: 'Urban Street', promptText: 'on this urban street setting with natural daylight' },
      { id: 3, name: 'Nature Park', promptText: 'on this outdoor park setting with natural greenery' }
    ]
  }
] as const;

export const DEFAULT_GENERATION_SETTINGS = {
  seed: null as number | null,
  cfg: 7.5 as number,
  fluxGuidance: 0.7 as number,
  numImages: 4 as number,
  ratio: '9:16' as string,
  imageFormat: 'jpeg' as string,
  apiDelay: 500 as number, // Default 500ms delay between API calls
};

export const ASPECT_RATIOS = [
  { value: '1:1', label: '1:1 (1024x1024)' },
  { value: '4:5', label: '4:5 (819x1024)' },
  { value: '9:16', label: '9:16 (576x1024)' },
  { value: '16:9', label: '16:9 (1024x576)' },
  { value: '5:4', label: '5:4 (1024x819)' },
  { value: '3:2', label: '3:2 (1024x683)' },
  { value: '2:1', label: '2:1 (1024x512)' },
] as const;

export const IMAGE_FORMATS = [
  { value: 'jpeg', label: 'JPEG (smaller size)' },
  { value: 'png', label: 'PNG (transparency)' },
  { value: 'webp', label: 'WebP (modern)' },
] as const;

export const NUM_IMAGES_OPTIONS = [1, 2, 4, 8] as const;

// API delay options in milliseconds
export const API_DELAY_OPTIONS = [
  { value: 0, label: 'No delay (0ms)', description: 'Fastest, but may hit rate limits' },
  { value: 250, label: 'Fast (250ms)', description: 'Good for small batches' },
  { value: 500, label: 'Normal (500ms)', description: 'Recommended default' },
  { value: 1000, label: 'Safe (1s)', description: 'Conservative, avoids rate limits' },
  { value: 2000, label: 'Slow (2s)', description: 'Very conservative' },
] as const;

export const BLOCK_COLORS = {
  model: { bg: 'bg-blue-100', text: 'text-blue-900', border: 'border-blue-200' },
  angle: { bg: 'bg-green-100', text: 'text-green-900', border: 'border-green-200' },
  garment: { bg: 'bg-purple-100', text: 'text-purple-900', border: 'border-purple-200' },
  mainGarment: { bg: 'bg-indigo-100', text: 'text-indigo-900', border: 'border-indigo-200' },
  background: { bg: 'bg-orange-100', text: 'text-orange-900', border: 'border-orange-200' },
  artDirection: { bg: 'bg-pink-100', text: 'text-pink-900', border: 'border-pink-200' },
} as const;

export type Model = typeof CAMPAIGN_MODELS[number];
export type AngleBlock = typeof ANGLE_BLOCKS[number];
export type SettingBlock = typeof SETTING_BLOCKS[number];