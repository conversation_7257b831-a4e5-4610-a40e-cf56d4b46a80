import React, { useState, useEffect } from "react";
import { Toaster } from "./components/ui/toaster";
import { Toaster as Sonner } from "./components/ui/sonner";
import { TooltipProvider } from "./components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { Toaster as HotToaster } from "react-hot-toast";
import { 
  BrowserRouter, 
  Routes, 
  Route,
  Navigate,
  useNavigate
} from "react-router-dom";
import { SupabaseProvider, useSupabase } from "./contexts/SupabaseContext";
import { UserRoleProvider, useUserRole } from "./contexts/UserRoleContext";
import { OrganizationProvider } from "./contexts/OrganizationContext";
import { ProfileProvider } from "./contexts/ProfileContext";
import { BackgroundQueueProvider } from "./contexts/BackgroundQueueContext";
import Layout from "./components/layout/Layout";
import ProtectedRoute from "./components/auth/ProtectedRoute";
import RoleProtectedRoute from "./components/auth/RoleProtectedRoute";
import Dashboard from "./pages/Dashboard";
import Organizations from "./pages/Organizations";
import OrganizationDetail from "./pages/OrganizationDetail";
import CollectionDetail from "./pages/CollectionDetail";
import AssetUpload from "./pages/AssetUpload";
import AssetDetail from "./pages/AssetDetail";
import { AssetDetailPage } from "./components/asset-detail/AssetDetailPage";
import Login from "./pages/Login";
import ResetPassword from "./pages/ResetPassword";
import UpdatePassword from "./pages/UpdatePassword";
import NotFound from "./pages/NotFound";
import ManualReset from "./pages/ManualReset";
import OrganizationCollections from "./pages/OrganizationCollections";
import OrganizationCollectionCreation from "./pages/OrganizationCollectionCreation";
import { RefactoredCollectionCreation } from "./components/collection-creation/RefactoredCollectionCreation";
import Profile from "./pages/Profile";
import OrganizationMembers from "./pages/OrganizationMembers";
import OrganizationSettings from "./pages/OrganizationSettings";
import BrandSettings from "./pages/BrandSettings";
import InvitationAccept from "./pages/InvitationAccept";
import BulkUploadWizard from "./pages/BulkUploadWizard";
import PlatformAdminUserManagement from "./pages/PlatformAdminUserManagement";
import PlatformAdminModelLibrary from "./pages/PlatformAdminModelLibrary";
import PlatformAdminModelLibraryDetail from "./pages/PlatformAdminModelLibraryDetail";
import { AssetCompareView } from "./components/asset-compare/AssetCompareView";
import { isFeatureEnabled } from "./utils/featureFlags";
import ImageGeneratorDemo from "./pages/demo/ImageGeneratorDemo";
import FashionLabAPITest from "./pages/demo/FashionLabAPITest";

// Placeholder imports for the new organization pages
// import OrganizationDashboard from "./pages/organization/OrganizationDashboard"; // Placeholder
// import OrganizationCollectionDetail from "./pages/organization/OrganizationCollectionDetail"; // Placeholder for adapted CollectionDetail
// import OrganizationAssetDetail from "./pages/organization/OrganizationAssetDetail"; // Placeholder for adapted AssetDetail

// Suppress React Router future flag warnings
const originalConsoleWarn = console.warn;
console.warn = function filterWarnings(msg, ...args) {
  if (msg.includes('React Router Future Flag warning')) {
    return;
  }
  originalConsoleWarn(msg, ...args);
};

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 0, // Consider data stale immediately
      gcTime: 5 * 60 * 1000, // 5 minutes (formerly cacheTime)
      refetchOnWindowFocus: true, // Refetch when window regains focus
      refetchOnMount: true, // Always refetch when component mounts
      refetchOnReconnect: true, // Refetch on reconnect
      retry: 1, // Retry failed requests once
      networkMode: 'always', // Always attempt to fetch, even offline
    },
    mutations: {
      networkMode: 'always',
    },
  },
});

const App = () => (
  <QueryClientProvider client={queryClient}>
    <SupabaseProvider>
      <UserRoleProvider>
        <OrganizationProvider>
          <ProfileProvider>
            <BackgroundQueueProvider>
              <TooltipProvider>
              <Toaster />
              <HotToaster position="top-right" />
              <Sonner />
              <BrowserRouter>
              <Routes>
                {/* Public routes */}
                <Route path="/login" element={<Login />} />
                <Route path="/reset-password" element={<ResetPassword />} />
                <Route path="/update-password" element={<UpdatePassword />} />
                <Route path="/manual-reset" element={<ManualReset />} />
                <Route path="/invite/:token" element={<InvitationAccept />} />
                
                {/* Redirect root to dashboard if authenticated, otherwise to login */}
                <Route path="/" element={<ProtectedRoute />}>
                  <Route path="/" element={<RoleBasedRedirect />} />
                </Route>
                
                {/* Admin routes */}
                <Route element={<RoleProtectedRoute allowedRoles={['platform_super', 'platform_admin', 'brand_admin', 'brand_member', 'external_retoucher', 'external_prompter']} redirectPath="/login" />}>
                  <Route element={<Layout />}>
                    <Route path="/dashboard" element={<Dashboard />} />
                    <Route path="/profile" element={<Profile />} />
                    <Route path="/admin/users" element={<PlatformAdminUserManagement />} />
                    <Route path="/admin/model-library" element={<PlatformAdminModelLibrary />} />
                    <Route path="/admin/model-library/:modelId" element={<PlatformAdminModelLibraryDetail />} />
                    
                    {/* Organization routes for admins (formerly client routes) */}
                    <Route path="/organizations" element={<Organizations />} />
                    <Route path="/organizations/:id" element={<OrganizationDetail />} />
                    
                    {/* --- Organization Context Routes --- */}
                    {/* Route for listing collections within an organization */}
                    <Route path="/organizations/:orgId/collections" element={<OrganizationCollections />} />

                    {/* Route for organization members management */}
                    <Route path="/organizations/:id/members" element={<OrganizationMembers />} />

                    {/* Route for organization settings */}
                    <Route path="/organizations/:id/settings" element={<OrganizationSettings />} />

                    {/* Route for bulk upload wizard */}
                    <Route path="/organizations/:orgId/bulk-upload" element={<BulkUploadWizard />} />

                    {/* Route for creating a new collection in an organization */}
                    <Route 
                      path="/organizations/:orgId/collections/new" 
                      element={
                        isFeatureEnabled('ENHANCED_COLLECTION_BRIEF') 
                          ? <RefactoredCollectionCreation /> 
                          : <OrganizationCollectionCreation />
                      } 
                    />

                    {/* LEGACY ROUTE: Old collection creation component (kept for reference) */}
                    {isFeatureEnabled('ENHANCED_COLLECTION_BRIEF') && (
                      <Route path="/organizations/:orgId/collections/new-legacy" element={<OrganizationCollectionCreation />} />
                    )}

                    {/* Route for viewing a specific collection within an organization */}
                    <Route path="/organizations/:orgId/collections/:collectionId" element={<CollectionDetail />} />

                    {/* Route for Asset Compare View within an organization's collection */}
                    {isFeatureEnabled('ASSET_COMPARE_VIEW') && (
                      <Route path="/organizations/:orgId/collections/:collectionId/compare" element={<AssetCompareView />} />
                    )}

                    {/* Route for uploading assets to an organization's collection */}
                    <Route path="/organizations/:orgId/collections/:collectionId/upload" element={<AssetUpload />} />

                    {/* Route for viewing a specific asset within an organization's collection */}
                    <Route path="/organizations/:orgId/collections/:collectionId/assets/:assetId" element={<AssetDetail />} />
                    
                    {/* Route for AI image generation within an organization's collection */}
                    <Route path="/organizations/:orgId/collections/:collectionId/generate" element={<ImageGeneratorDemo />} />
                    
                    {/* Fashion Lab API Test Page */}
                    <Route path="/test/fashion-lab-api" element={<FashionLabAPITest />} />
                    <Route path="/test/fashion-lab-api/:collectionId" element={<FashionLabAPITest />} />
                  </Route>
                </Route>
                
                {/* Catch all route */}
                <Route path="*" element={<NotFound />} />
              </Routes>
            </BrowserRouter>
              </TooltipProvider>
            </BackgroundQueueProvider>
          </ProfileProvider>
        </OrganizationProvider>
      </UserRoleProvider>
    </SupabaseProvider>
  </QueryClientProvider>
);

// Component to redirect based on role
const RoleBasedRedirect = () => {
  const { userRole, isLoadingRole } = useUserRole();
  const { user, isLoadingUser } = useSupabase();
  const navigate = useNavigate();
  const [redirectAttempts, setRedirectAttempts] = useState(0);
  
  // Add detailed debugging
  console.log(`RoleBasedRedirect[${redirectAttempts}]: userRole=${userRole}, isLoadingRole=${isLoadingRole}, userExists=${!!user}, isLoadingUser=${isLoadingUser}`);
  
  // Force redirect to dashboard when conditions are met
  useEffect(() => {
    if (!isLoadingUser && user && !isLoadingRole) {
      if (userRole) {
        // We have a role, redirect to dashboard
        console.log(`RoleBasedRedirect: User has role '${userRole}', redirecting to dashboard...`);
        navigate('/dashboard', { replace: true });
      } else if (redirectAttempts < 3) {
        // No role yet but user exists, wait briefly and try again
        console.log(`RoleBasedRedirect: User exists but no role yet. Attempt ${redirectAttempts + 1}/3`);
        const timer = setTimeout(() => {
          setRedirectAttempts(prev => prev + 1);
        }, 500);
        return () => clearTimeout(timer);
      } else {
        // After 3 attempts with no role, redirect to login
        console.log(`RoleBasedRedirect: No role found after ${redirectAttempts} attempts, redirecting to login`);
        navigate('/login', { replace: true });
      }
    } else if (!isLoadingUser && !user) {
      // Not logged in, redirect to login
      console.log(`RoleBasedRedirect: No user found, redirecting to login`);
      navigate('/login', { replace: true });
    }
    // Only include dependencies that should trigger a redirect
  }, [user, userRole, isLoadingUser, isLoadingRole, redirectAttempts, navigate]);
  
  // Show loading indicator while figuring out where to redirect
  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gray-50">
      <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mb-4"></div>
      <p className="text-gray-500">Preparing your dashboard...</p>
      {redirectAttempts > 0 && <p className="text-xs text-gray-400">Attempt {redirectAttempts}/3</p>}
    </div>
  );
};

export default App;
