import React, { useState } from 'react';
import { useBackgroundQueue, useBatch } from '../../contexts/BackgroundQueueContext';
import { GenerationBatch } from '../../services/backgroundQueueManager';
import { Clock, CheckCircle, XCircle, Loader2, Eye, EyeOff, Trash2, ChevronDown, ChevronUp, Activity } from 'lucide-react';
import { ForceStopButton } from './ForceStopButton';

interface QueueDashboardProps {
  collectionId?: string;
  className?: string;
}

export function QueueDashboard({ collectionId, className = '' }: QueueDashboardProps) {
  const { batches, activeBatches, cleanup } = useBackgroundQueue();
  const [showCompleted, setShowCompleted] = useState(false);
  const [expandedBatches, setExpandedBatches] = useState<Set<string>>(new Set());
  const [isCollapsed, setIsCollapsed] = useState(true); // Start collapsed by default

  // Filter batches by collection if specified
  const filteredBatches = collectionId 
    ? batches.filter(batch => batch.collectionId === collectionId)
    : batches;

  const activeBatchesFiltered = collectionId
    ? activeBatches.filter(batch => batch.collectionId === collectionId)
    : activeBatches;

  const completedBatches = filteredBatches.filter(batch => 
    batch.status === 'completed' || batch.status === 'failed' || batch.status === 'partial'
  );

  const toggleBatchExpansion = (batchId: string) => {
    const newExpanded = new Set(expandedBatches);
    if (newExpanded.has(batchId)) {
      newExpanded.delete(batchId);
    } else {
      newExpanded.add(batchId);
    }
    setExpandedBatches(newExpanded);
  };

  // If no batches, don't show anything
  if (filteredBatches.length === 0) {
    return null;
  }

  // Compact summary view when collapsed
  if (isCollapsed) {
    return (
      <div className={`bg-white rounded-lg border border-gray-200 ${className}`}>
        <div
          className="p-4 cursor-pointer hover:bg-gray-50 transition-colors"
          onClick={() => setIsCollapsed(false)}
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Activity className="w-5 h-5 text-gray-600" />
              <div>
                <h3 className="text-sm font-medium text-gray-900">Generation Queue</h3>
                <p className="text-xs text-gray-500">
                  {activeBatchesFiltered.length > 0 ? (
                    <>
                      {activeBatchesFiltered.length} active generation{activeBatchesFiltered.length !== 1 ? 's' : ''} running
                      {completedBatches.length > 0 && `, ${completedBatches.length} completed`}
                    </>
                  ) : completedBatches.length > 0 ? (
                    `${completedBatches.length} completed generation${completedBatches.length !== 1 ? 's' : ''}`
                  ) : (
                    'No active generations'
                  )}
                </p>
              </div>
            </div>

            <div className="flex items-center gap-2">
              {/* Show active batch progress indicators */}
              {activeBatchesFiltered.slice(0, 3).map(batch => {
                const progress = (batch.completedCount / batch.totalCount) * 100;
                return (
                  <div key={batch.id} className="flex items-center gap-1">
                    <div className="w-8 h-1.5 bg-gray-200 rounded-full overflow-hidden">
                      <div
                        className="h-full bg-blue-500 transition-all duration-300"
                        style={{ width: `${progress}%` }}
                      />
                    </div>
                    <span className="text-xs text-gray-500">{batch.completedCount}/{batch.totalCount}</span>
                  </div>
                );
              })}
              {activeBatchesFiltered.length > 3 && (
                <span className="text-xs text-gray-500">+{activeBatchesFiltered.length - 3} more</span>
              )}

              <ChevronDown className="w-4 h-4 text-gray-400" />
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Expanded view (original functionality)
  return (
    <div className={`bg-white rounded-lg border border-gray-200 ${className}`}>
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Activity className="w-5 h-5 text-gray-600" />
            <h3 className="text-lg font-semibold text-gray-900">Generation Queue</h3>
          </div>
          <div className="flex items-center gap-3">
            <ForceStopButton className="mr-2" />
            <button
              onClick={() => setIsCollapsed(true)}
              className="flex items-center gap-2 px-3 py-1 text-sm text-gray-600 hover:text-gray-900 border border-gray-300 rounded-md hover:bg-gray-50"
            >
              <ChevronUp className="w-4 h-4" />
              Collapse
            </button>
            {completedBatches.length > 0 && (
              <button
                onClick={() => setShowCompleted(!showCompleted)}
                className="flex items-center gap-2 px-3 py-1 text-sm text-gray-600 hover:text-gray-900 border border-gray-300 rounded-md hover:bg-gray-50"
              >
                {showCompleted ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                {showCompleted ? 'Hide' : 'Show'} Completed ({completedBatches.length})
              </button>
            )}
            {completedBatches.length > 5 && (
              <button
                onClick={cleanup}
                className="flex items-center gap-2 px-3 py-1 text-sm text-gray-600 hover:text-red-600 border border-gray-300 rounded-md hover:bg-red-50"
              >
                <Trash2 className="w-4 h-4" />
                Cleanup Old
              </button>
            )}
          </div>
        </div>

        {activeBatchesFiltered.length > 0 && (
          <div className="mt-3 text-sm text-gray-600">
            {activeBatchesFiltered.length} active generation{activeBatchesFiltered.length !== 1 ? 's' : ''} running in background
          </div>
        )}
      </div>

      <div className="divide-y divide-gray-200">
        {/* Active Batches */}
        {activeBatchesFiltered.map(batch => (
          <BatchCard
            key={batch.id}
            batch={batch}
            isExpanded={expandedBatches.has(batch.id)}
            onToggleExpand={() => toggleBatchExpansion(batch.id)}
          />
        ))}

        {/* Completed Batches */}
        {showCompleted && completedBatches.map(batch => (
          <BatchCard
            key={batch.id}
            batch={batch}
            isExpanded={expandedBatches.has(batch.id)}
            onToggleExpand={() => toggleBatchExpansion(batch.id)}
          />
        ))}
      </div>
    </div>
  );
}

interface BatchCardProps {
  batch: GenerationBatch;
  isExpanded: boolean;
  onToggleExpand: () => void;
}

function BatchCard({ batch, isExpanded, onToggleExpand }: BatchCardProps) {
  const { progress } = useBatch(batch.id);
  
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="w-5 h-5 text-yellow-500" />;
      case 'generating':
        return <Loader2 className="w-5 h-5 text-blue-500 animate-spin" />;
      case 'completed':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'partial':
        return <CheckCircle className="w-5 h-5 text-yellow-500" />;
      case 'failed':
        return <XCircle className="w-5 h-5 text-red-500" />;
      default:
        return <Clock className="w-5 h-5 text-gray-400" />;
    }
  };

  const getStatusText = (batch: GenerationBatch) => {
    switch (batch.status) {
      case 'pending':
        return 'Starting...';
      case 'generating':
        return `Generating... ${batch.completedCount}/${batch.totalCount} completed`;
      case 'completed':
        return `Completed successfully (${batch.completedCount} images)`;
      case 'partial':
        return `Partially completed (${batch.completedCount}/${batch.totalCount} images)`;
      case 'failed':
        return 'Generation failed';
      default:
        return 'Unknown status';
    }
  };

  const overallProgress = progress?.overallProgress || 0;
  const isActive = batch.status === 'pending' || batch.status === 'generating';

  return (
    <div className="p-6">
      <div 
        className="flex items-center justify-between cursor-pointer"
        onClick={onToggleExpand}
      >
        <div className="flex items-center gap-4 flex-1">
          {getStatusIcon(batch.status)}
          
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-3 mb-1">
              <h4 className="font-medium text-gray-900 truncate">
                {batch.prompt.length > 60 ? `${batch.prompt.substring(0, 60)}...` : batch.prompt}
              </h4>
              <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                {batch.totalCount} images
              </span>
            </div>
            
            <p className="text-sm text-gray-600 mb-2">
              {getStatusText(batch)}
            </p>
            
            {isActive && (
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${overallProgress}%` }}
                />
              </div>
            )}
          </div>
        </div>

        <div className="flex items-center gap-3 text-sm text-gray-500">
          <span>{new Date(batch.createdAt).toLocaleTimeString()}</span>
          <span className={`transform transition-transform ${isExpanded ? 'rotate-180' : ''}`}>
            ▼
          </span>
        </div>
      </div>

      {isExpanded && (
        <div className="mt-4 pl-9">
          <div className="bg-gray-50 rounded-lg p-4">
            <h5 className="font-medium text-gray-900 mb-3">Individual Images</h5>
            
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
              {batch.queues.map((queue, index) => (
                <div 
                  key={queue.queueId || index}
                  className="flex items-center gap-3 p-3 bg-white rounded border"
                >
                  <div className="flex-shrink-0">
                    {queue.status === 'pending' && <Clock className="w-4 h-4 text-yellow-500" />}
                    {queue.status === 'generating' && <Loader2 className="w-4 h-4 text-blue-500 animate-spin" />}
                    {queue.status === 'completed' && <CheckCircle className="w-4 h-4 text-green-500" />}
                    {queue.status === 'failed' && <XCircle className="w-4 h-4 text-red-500" />}
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between mb-1">
                      <span className="text-sm font-medium text-gray-900">
                        Image {queue.imageIndex}
                      </span>
                      {queue.status === 'generating' && (
                        <span className="text-xs text-gray-500">
                          {queue.progress}%
                        </span>
                      )}
                    </div>
                    
                    {queue.status === 'generating' && (
                      <div className="w-full bg-gray-200 rounded-full h-1">
                        <div 
                          className="bg-blue-500 h-1 rounded-full transition-all duration-300"
                          style={{ width: `${queue.progress}%` }}
                        />
                      </div>
                    )}
                    
                    {queue.error && (
                      <p className="text-xs text-red-600 mt-1">{queue.error}</p>
                    )}
                    
                    {queue.completedAt && (
                      <p className="text-xs text-gray-500 mt-1">
                        Completed at {new Date(queue.completedAt).toLocaleTimeString()}
                      </p>
                    )}
                  </div>
                </div>
              ))}
            </div>

            <div className="mt-4 text-sm text-gray-600">
              <p><strong>Prompt:</strong> {batch.prompt}</p>
              <p><strong>Settings:</strong> {batch.settings.numImages} images, {batch.settings.apiDelay}ms delay</p>
              {batch.metadata && Object.keys(batch.metadata).length > 0 && (
                <p><strong>Metadata:</strong> {JSON.stringify(batch.metadata, null, 2)}</p>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
