import React from 'react';
import { Progress } from '../ui/progress';
import { CheckCircle, XCircle, Clock, Loader2 } from 'lucide-react';
import { cn } from '../common/utils/utils';
import type { QueueProgress } from './types';

interface ProgressTrackerProps {
  queueProgress: QueueProgress[];
  overallProgress: number;
  className?: string;
}

export function ProgressTracker({ queueProgress, overallProgress, className }: ProgressTrackerProps) {
  if (queueProgress.length === 0) {
    return null;
  }

  const getStatusIcon = (status: QueueProgress['status']) => {
    switch (status) {
      case 'pending':
        return <Clock className="w-4 h-4 text-muted-foreground" />;
      case 'generating':
        return <Loader2 className="w-4 h-4 text-blue-500 animate-spin" />;
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'failed':
        return <XCircle className="w-4 h-4 text-red-500" />;
      default:
        return <Clock className="w-4 h-4 text-muted-foreground" />;
    }
  };

  const getStatusColor = (status: QueueProgress['status']) => {
    switch (status) {
      case 'pending':
        return 'text-muted-foreground';
      case 'generating':
        return 'text-blue-600';
      case 'completed':
        return 'text-green-600';
      case 'failed':
        return 'text-red-600';
      default:
        return 'text-muted-foreground';
    }
  };

  const completedCount = queueProgress.filter(q => q.status === 'completed').length;
  const failedCount = queueProgress.filter(q => q.status === 'failed').length;
  const totalCount = queueProgress.length;

  return (
    <div className={cn("space-y-4", className)}>
      {/* Overall Progress */}
      <div className="space-y-2">
        <div className="flex items-center justify-between text-sm">
          <span className="font-medium">Overall Progress</span>
          <span className="text-muted-foreground">
            {completedCount}/{totalCount} completed
            {failedCount > 0 && `, ${failedCount} failed`}
          </span>
        </div>
        <Progress value={overallProgress} className="h-2" />
        <div className="text-xs text-muted-foreground text-center">
          {overallProgress.toFixed(0)}% complete
        </div>
      </div>

      {/* Individual Image Progress */}
      <div className="space-y-2">
        <div className="text-sm font-medium">Individual Images</div>
        <div className="grid grid-cols-2 gap-2">
          {queueProgress.map((queue, index) => (
            <div
              key={queue.queueId || index}
              className="flex items-center space-x-2 p-2 rounded-md border bg-card"
            >
              {getStatusIcon(queue.status)}
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between">
                  <span className={cn("text-sm font-medium", getStatusColor(queue.status))}>
                    Image {queue.imageIndex}
                  </span>
                  {queue.status === 'generating' && (
                    <span className="text-xs text-muted-foreground">
                      {queue.progress.toFixed(0)}%
                    </span>
                  )}
                </div>
                {queue.status === 'generating' && (
                  <Progress value={queue.progress} className="h-1 mt-1" />
                )}
                {queue.status === 'failed' && queue.error && (
                  <div className="text-xs text-red-500 truncate mt-1" title={queue.error}>
                    {queue.error}
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Status Summary */}
      {queueProgress.some(q => q.status === 'generating' || q.status === 'pending') && (
        <div className="text-center text-sm text-muted-foreground">
          {queueProgress.some(q => q.status === 'generating') 
            ? 'Generating images...' 
            : 'Preparing to generate images...'}
        </div>
      )}
    </div>
  );
}
