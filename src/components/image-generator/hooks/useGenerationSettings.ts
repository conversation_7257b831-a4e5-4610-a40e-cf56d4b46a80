import { useState, useCallback } from 'react';
import type { GenerationSettings } from '../types';

const defaultSettings: GenerationSettings = {
  prompt: '',
  negativePrompt: 'blurry, bad quality, distorted, deformed, ugly, pixelated, low resolution',
  width: 1920,
  height: 1080,
  steps: 20,
  cfg: 7.5,
  seed: null,
  sampler: 'Euler a',
  clipSkip: 1,
  loraWeight: 1.0,
  fluxGuidance: 0.7,
  numImages: 4,
  ratio: '9:16',
  imageFormat: 'jpeg',
  apiDelay: 500, // Default 500ms delay between API calls
};

export function useGenerationSettings() {
  const [settings, setSettings] = useState<GenerationSettings>(defaultSettings);
  const [isAdvancedMode, setIsAdvancedMode] = useState(false);

  const updateSetting = useCallback(<K extends keyof GenerationSettings>(
    key: K,
    value: GenerationSettings[K]
  ) => {
    setSettings(prev => ({
      ...prev,
      [key]: value,
    }));
  }, []);

  const resetSettings = useCallback(() => {
    setSettings(defaultSettings);
  }, []);

  const toggleAdvancedMode = useCallback(() => {
    setIsAdvancedMode(prev => !prev);
  }, []);

  // Update ratio and adjust dimensions accordingly
  const updateRatio = useCallback((ratio: string) => {
    let width = 1920;
    let height = 1080;
    
    switch (ratio) {
      case '1:1':
        width = 1024;
        height = 1024;
        break;
      case '4:3':
        width = 1024;
        height = 768;
        break;
      case '3:4':
        width = 768;
        height = 1024;
        break;
      case '16:9':
        width = 1920;
        height = 1080;
        break;
      case '9:16':
        width = 1080;
        height = 1920;
        break;
    }
    
    setSettings(prev => ({
      ...prev,
      ratio,
      width,
      height,
    }));
  }, []);

  // Update batch of settings at once
  const updateBatchSettings = useCallback((updates: Partial<GenerationSettings>) => {
    setSettings(prev => ({
      ...prev,
      ...updates,
    }));
  }, []);

  return {
    settings,
    isAdvancedMode,
    updateSetting,
    resetSettings,
    toggleAdvancedMode,
    updateRatio,
    updateBatchSettings,
  };
}