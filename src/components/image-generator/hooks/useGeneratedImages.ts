import { useState, useCallback, useEffect } from 'react';
import { useToast } from '../../../components/ui/use-toast';
import { supabase } from '../../../components/common/utils/supabase';
import type { GeneratedImage } from '../types';

export function useGeneratedImages(collectionId?: string) {
  const [generatedImages, setGeneratedImages] = useState<GeneratedImage[]>([]);
  const [selectedImages, setSelectedImages] = useState<Set<string>>(new Set());
  const [isLoadingImages, setIsLoadingImages] = useState(false);
  const { toast } = useToast();

  // Load existing AI-generated images from database
  useEffect(() => {
    if (collectionId) {
      loadExistingImages();
    }
  }, [collectionId]);

  const loadExistingImages = async () => {
    if (!collectionId) return;
    
    setIsLoadingImages(true);
    try {
      // First, try to fetch images with their batch information
      const { data: aiImages, error } = await supabase
        .from('ai_generated_images')
        .select(`
          *,
          batch:batch_table_id (
            id,
            batch_id,
            prompt,
            status,
            total_images,
            completed_images,
            failed_images,
            metadata,
            settings,
            created_at
          )
        `)
        .eq('collection_id', collectionId)
        .order('created_at', { ascending: false });

      if (error) {
        // Fallback to simple query if the batch table doesn't exist yet
        console.warn('Failed to fetch with batch info, falling back to simple query:', error);
        const { data: simpleImages, error: simpleError } = await supabase
          .from('ai_generated_images')
          .select('*')
          .eq('collection_id', collectionId)
          .order('created_at', { ascending: false });

        if (simpleError) throw simpleError;

        if (simpleImages && simpleImages.length > 0) {
          const formattedImages: GeneratedImage[] = simpleImages.map(img => {
            // Construct the full URL from storage_path
            let imageUrl = '';
            if (img.storage_path) {
              // If we have a storage_path, construct the URL from it
              const { data } = supabase.storage.from('ai-generated').getPublicUrl(img.storage_path);
              imageUrl = data.publicUrl;
            } else if (img.image_url) {
              // Fallback to image_url if it exists
              imageUrl = img.image_url;
            }
            
            return {
              id: img.id,
              url: imageUrl,
              thumbnail: imageUrl, // AI generated images don't have separate thumbnails
              prompt: img.prompt || '',
              // Try to get model and angle from metadata first, then fallback to top-level fields
              model: img.metadata?.model_name || img.metadata?.modelName || img.model_used || 'AI Model',
              modelId: img.metadata?.modelId || img.model_id || '',
              angle: img.metadata?.angle || img.angle || 'front',
              timestamp: new Date(img.created_at),
              metadata: img.metadata || {},
              isFromDatabase: true,
            };
          });
          
          setGeneratedImages(formattedImages);
        }
        return;
      }

      if (aiImages && aiImages.length > 0) {
        const formattedImages: GeneratedImage[] = aiImages.map(img => {
          // Construct the full URL from storage_path
          let imageUrl = '';
          if (img.storage_path) {
            // If we have a storage_path, construct the URL from it
            const { data } = supabase.storage.from('ai-generated').getPublicUrl(img.storage_path);
            imageUrl = data.publicUrl;
          } else if (img.image_url) {
            // Fallback to image_url if it exists
            imageUrl = img.image_url;
          }
          
          // Include batch info in metadata if available
          const enhancedMetadata = {
            ...img.metadata,
            // Use the database batch info if available
            batch: img.batch,
            batchTableId: img.batch_table_id,
          };
          
          return {
            id: img.id,
            url: imageUrl,
            thumbnail: imageUrl, // AI generated images don't have separate thumbnails
            prompt: img.prompt || '',
            // Try to get model and angle from metadata first, then fallback to top-level fields
            model: img.metadata?.model_name || img.metadata?.modelName || img.model_used || 'AI Model',
            modelId: img.metadata?.modelId || img.model_id || '',
            angle: img.metadata?.angle || img.angle || 'front',
            timestamp: new Date(img.created_at),
            metadata: enhancedMetadata,
            isFromDatabase: true,
          };
        });
        
        setGeneratedImages(formattedImages);
      }
    } catch (error) {
      console.error('Error loading AI images:', error);
      toast({
        title: "Error loading images",
        description: "Failed to load existing AI-generated images",
        variant: "destructive",
      });
    } finally {
      setIsLoadingImages(false);
    }
  };

  const addGeneratedImage = useCallback((image: GeneratedImage) => {
    setGeneratedImages(prev => [image, ...prev]);
  }, []);

  const toggleImageSelection = useCallback((imageId: string) => {
    setSelectedImages(prev => {
      const newSet = new Set(prev);
      if (newSet.has(imageId)) {
        newSet.delete(imageId);
      } else {
        newSet.add(imageId);
      }
      return newSet;
    });
  }, []);

  const selectAllImages = useCallback(() => {
    setSelectedImages(new Set(generatedImages.map(img => img.id)));
  }, [generatedImages]);

  const clearSelection = useCallback(() => {
    setSelectedImages(new Set());
  }, []);

  const deleteSelectedImages = useCallback(async () => {
    const imagesToDelete = Array.from(selectedImages);
    
    if (imagesToDelete.length === 0) return;
    
    // Track failed deletions
    const failedDeletions: string[] = [];
    
    // Get the images to delete from database
    const dbImages = generatedImages.filter(
      img => img.isFromDatabase && imagesToDelete.includes(img.id)
    );
    
    if (dbImages.length > 0) {
      // First, try to delete files from storage
      for (const img of dbImages) {
        // Get storage path from metadata or construct it
        let storagePath: string | null = null;
        
        // First check if we have a storage_path in the database record
        const { data: imageRecord } = await supabase
          .from('ai_generated_images')
          .select('storage_path')
          .eq('id', img.id)
          .single();
        
        if (imageRecord?.storage_path) {
          storagePath = imageRecord.storage_path;
        }
        
        // Try to delete from storage if we have a path
        if (storagePath) {
          const { error: storageError } = await supabase.storage
            .from('ai-generated')
            .remove([storagePath]);
          
          if (storageError) {
            console.error(`Failed to delete file from storage: ${storagePath}`, storageError);
            // Continue with database deletion even if storage fails
          }
        }
      }
      
      // Delete from database
      const { error: dbError } = await supabase
        .from('ai_generated_images')
        .delete()
        .in('id', dbImages.map(img => img.id));
      
      if (dbError) {
        console.error('Database deletion error:', dbError);
        toast({
          title: "Error deleting images",
          description: dbError.message || "Some images could not be deleted from the database",
          variant: "destructive",
        });
        return;
      }
    }
    
    // Remove from local state (including non-database images)
    setGeneratedImages(prev => 
      prev.filter(img => !imagesToDelete.includes(img.id))
    );
    setSelectedImages(new Set());
    
    toast({
      title: "Images deleted",
      description: `${imagesToDelete.length} image(s) deleted successfully`,
    });
  }, [selectedImages, generatedImages, toast]);

  return {
    generatedImages,
    selectedImages,
    isLoadingImages,
    addGeneratedImage,
    toggleImageSelection,
    selectAllImages,
    clearSelection,
    deleteSelectedImages,
    refreshImages: loadExistingImages,
  };
}