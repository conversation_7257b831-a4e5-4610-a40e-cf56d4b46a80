import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTitle } from '../../../components/ui/dialog';
import { Button } from '../../../components/ui/button';
import { Badge } from '../../../components/ui/badge';
import { Download, Copy } from 'lucide-react';
import { useToast } from '../../../components/ui/use-toast';
import type { GeneratedImage } from '../types';

interface ImageDetailDialogProps {
  image: GeneratedImage | null;
  isOpen: boolean;
  onClose: () => void;
}

export function ImageDetailDialog({ image, isOpen, onClose }: ImageDetailDialogProps) {
  const { toast } = useToast();

  if (!image) return null;

  // Helper functions for formatting
  const formatExecutionTime = (ms: number): string => {
    if (ms < 60000) return `${(ms / 1000).toFixed(1)} sec`;
    return `${(ms / 60000).toFixed(1)} min`;
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes < 1024) return `${bytes} B`;
    if (bytes < 1048576) return `${(bytes / 1024).toFixed(0)} KB`;
    return `${(bytes / 1048576).toFixed(1)} MB`;
  };

  const formatFieldName = (key: string): string => {
    const formatted = key
      .replace(/_/g, ' ')
      .replace(/([A-Z])/g, ' $1')
      .trim();
    return formatted.charAt(0).toUpperCase() + formatted.slice(1);
  };

  const formatMetadataValue = (key: string, value: any): string => {
    // Special handling for known fields
    if ((key === 'generation_time' || key === 'executionTime') && typeof value === 'number') {
      return formatExecutionTime(value);
    }
    if (key === 'file_size' && typeof value === 'number') {
      return formatFileSize(value);
    }
    if (key === 'activeBlocks' && Array.isArray(value)) {
      // Extract names from block objects
      return value.map(block => 
        typeof block === 'object' ? (block.name || 'Unknown') : block
      ).join(', ');
    }
    if (typeof value === 'object' && value !== null) {
      if (Array.isArray(value)) {
        // Handle arrays of objects
        return value.map(item => 
          typeof item === 'object' ? (item.name || JSON.stringify(item)) : item
        ).join(', ');
      }
      // For other objects, return a simplified version
      return 'Complex data';
    }
    return String(value);
  };

  // Fields to hide from display
  const HIDDEN_FIELDS = [
    'fashion_lab_original_url',
    'processed_from_ai_bucket', 
    'original_ai_storage_path',
    'ai_generated_image_id'
  ];

  const handleDownload = async () => {
    try {
      const response = await fetch(image.url);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${image.model}_${image.angle}_${image.id}.png`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      
      toast({
        title: "Image downloaded",
        description: "The image has been downloaded to your device",
      });
    } catch (error) {
      toast({
        title: "Download failed",
        description: "Failed to download the image",
        variant: "destructive",
      });
    }
  };

  const handleCopyPrompt = () => {
    navigator.clipboard.writeText(image.prompt);
    toast({
      title: "Prompt copied",
      description: "The prompt has been copied to your clipboard",
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Image Details</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4">
          {/* Image - Vertical aspect ratio (285:355 ≈ 4:5) */}
          <div className="relative bg-gray-100 rounded-lg overflow-hidden flex justify-center">
            <div className="relative w-full max-w-md">
              <div className="aspect-[4/5] w-full">
                <img
                  src={image.url}
                  alt={`${image.model} - ${image.angle}`}
                  className="absolute inset-0 w-full h-full object-cover rounded-lg"
                />
              </div>
            </div>
          </div>

          {/* Image Info */}
          <div className="space-y-3">
            <div className="flex items-center gap-2 flex-wrap">
              <Badge>{image.model}</Badge>
              <Badge variant="secondary">{image.angle}</Badge>
              {image.isFromDatabase && (
                <Badge variant="outline" className="text-green-600">
                  Saved
                </Badge>
              )}
            </div>

            {/* Prompt */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <h4 className="text-sm font-medium">Prompt</h4>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleCopyPrompt}
                  className="h-7"
                >
                  <Copy className="h-3 w-3 mr-1" />
                  Copy
                </Button>
              </div>
              <p className="text-sm text-gray-600 bg-gray-50 p-3 rounded">
                {image.prompt}
              </p>
            </div>

            {/* Metadata */}
            {image.metadata && Object.keys(image.metadata).length > 0 && (
              <div className="space-y-2">
                <h4 className="text-sm font-medium">Technical Details</h4>
                <div className="text-sm text-gray-600 bg-gray-50 p-3 rounded space-y-1">
                  {Object.entries(image.metadata)
                    .filter(([key]) => !HIDDEN_FIELDS.includes(key))
                    .sort(([a], [b]) => {
                      // Sort important fields to top
                      const priority = ['generation_time', 'executionTime', 'model_name', 'angle'];
                      const aIndex = priority.indexOf(a);
                      const bIndex = priority.indexOf(b);
                      if (aIndex !== -1 && bIndex !== -1) return aIndex - bIndex;
                      if (aIndex !== -1) return -1;
                      if (bIndex !== -1) return 1;
                      return a.localeCompare(b);
                    })
                    .map(([key, value]) => (
                      <div key={key} className="flex justify-between gap-4">
                        <span className="font-medium flex-shrink-0">
                          {formatFieldName(key)}:
                        </span>
                        <span className="text-right break-all">
                          {formatMetadataValue(key, value)}
                        </span>
                      </div>
                    ))}
                </div>
              </div>
            )}

            {/* Actions */}
            <div className="flex justify-end gap-2 pt-2">
              <Button
                variant="outline"
                onClick={handleDownload}
              >
                <Download className="h-4 w-4 mr-2" />
                Download
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}