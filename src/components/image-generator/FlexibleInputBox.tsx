import React, { useState, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Label } from '../ui/label';
import { Input } from '../ui/input';
import { Badge } from '../ui/badge';
import { Textarea } from '../ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Plus, X, Upload, Image as ImageIcon, Type, Palette } from 'lucide-react';
import { cn } from '../common/utils/utils';

export interface FlexibleInput {
  id: string;
  type: 'image' | 'text' | 'color';
  label: string;
  value: string;
  file?: File;
  preview?: string;
  base64?: string;
}

interface FlexibleInputBoxProps {
  inputs: FlexibleInput[];
  onInputsChange: (inputs: FlexibleInput[]) => void;
  maxInputs?: number;
  className?: string;
}

export function FlexibleInputBox({ 
  inputs, 
  onInputsChange, 
  maxInputs = 10,
  className 
}: FlexibleInputBoxProps) {
  const [isAddingInput, setIsAddingInput] = useState(false);
  const [newInputType, setNewInputType] = useState<'image' | 'text' | 'color'>('image');
  const [newInputLabel, setNewInputLabel] = useState('');

  const handleAddInput = () => {
    if (!newInputLabel.trim()) return;

    const newInput: FlexibleInput = {
      id: `input-${Date.now()}`,
      type: newInputType,
      label: newInputLabel,
      value: newInputType === 'color' ? '#000000' : ''
    };

    onInputsChange([...inputs, newInput]);
    setNewInputLabel('');
    setIsAddingInput(false);
  };

  const handleRemoveInput = (id: string) => {
    const input = inputs.find(i => i.id === id);
    if (input?.preview) {
      URL.revokeObjectURL(input.preview);
    }
    onInputsChange(inputs.filter(i => i.id !== id));
  };

  const handleImageUpload = useCallback(async (id: string, file: File) => {
    const preview = URL.createObjectURL(file);
    
    // Convert to base64
    const reader = new FileReader();
    reader.onloadend = () => {
      const base64 = reader.result as string;
      onInputsChange(inputs.map(input => 
        input.id === id 
          ? { ...input, file, preview, base64, value: file.name }
          : input
      ));
    };
    reader.readAsDataURL(file);
  }, [inputs, onInputsChange]);

  const handleValueChange = (id: string, value: string) => {
    onInputsChange(inputs.map(input => 
      input.id === id ? { ...input, value } : input
    ));
  };

  const getInputIcon = (type: FlexibleInput['type']) => {
    switch (type) {
      case 'image': return ImageIcon;
      case 'text': return Type;
      case 'color': return Palette;
    }
  };

  return (
    <Card className={cn("", className)}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-base">Extra Elements</CardTitle>
          {inputs.length < maxInputs && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsAddingInput(true)}
              className="h-7 text-xs"
            >
              <Plus className="w-3 h-3 mr-1" />
              Add Element
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent className="space-y-3">
        {inputs.length === 0 && !isAddingInput && (
          <div className="text-center py-8 text-muted-foreground">
            <div className="mb-2">
              <ImageIcon className="w-8 h-8 mx-auto text-gray-300" />
            </div>
            <p className="text-sm">No extra elements added</p>
            <p className="text-xs mt-1">Add garments, backgrounds, or other elements</p>
          </div>
        )}

        {/* Existing inputs */}
        {inputs.map((input) => {
          const Icon = getInputIcon(input.type);
          
          return (
            <div key={input.id} className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Icon className="w-4 h-4 text-muted-foreground" />
                  <Label className="text-sm font-medium">{input.label}</Label>
                  <Badge variant="secondary" className="text-xs">
                    {input.type}
                  </Badge>
                </div>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-6 w-6"
                  onClick={() => handleRemoveInput(input.id)}
                >
                  <X className="h-3 w-3" />
                </Button>
              </div>

              {input.type === 'image' && (
                <div>
                  {input.preview ? (
                    <div className="relative group">
                      <div className="aspect-[4/5] overflow-hidden rounded border bg-gray-100">
                        <img
                          src={input.preview}
                          alt={input.label}
                          className="w-full h-full object-cover"
                        />
                      </div>
                      <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center rounded">
                        <Button
                          variant="secondary"
                          size="sm"
                          onClick={() => handleRemoveInput(input.id)}
                        >
                          Remove
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <label className="block">
                      <input
                        type="file"
                        accept="image/*"
                        className="hidden"
                        onChange={(e) => {
                          const file = e.target.files?.[0];
                          if (file) handleImageUpload(input.id, file);
                        }}
                      />
                      <div className="border-2 border-dashed rounded-lg p-4 hover:border-gray-400 cursor-pointer transition-colors">
                        <div className="text-center">
                          <Upload className="w-6 h-6 mx-auto text-gray-400 mb-1" />
                          <p className="text-xs text-gray-500">Click to upload</p>
                        </div>
                      </div>
                    </label>
                  )}
                </div>
              )}

              {input.type === 'text' && (
                <Textarea
                  value={input.value}
                  onChange={(e) => handleValueChange(input.id, e.target.value)}
                  placeholder={`Enter ${input.label.toLowerCase()}`}
                  rows={2}
                  className="text-sm"
                />
              )}

              {input.type === 'color' && (
                <div className="flex items-center gap-2">
                  <input
                    type="color"
                    value={input.value}
                    onChange={(e) => handleValueChange(input.id, e.target.value)}
                    className="h-10 w-20 border rounded cursor-pointer"
                  />
                  <Input
                    value={input.value}
                    onChange={(e) => handleValueChange(input.id, e.target.value)}
                    className="flex-1"
                    placeholder="#000000"
                  />
                </div>
              )}
            </div>
          );
        })}

        {/* Add new input form */}
        {isAddingInput && (
          <div className="border rounded-lg p-3 space-y-3 bg-muted/30">
            <div className="grid grid-cols-2 gap-2">
              <div className="space-y-1">
                <Label className="text-xs">Type</Label>
                <Select value={newInputType} onValueChange={(v) => setNewInputType(v as any)}>
                  <SelectTrigger className="h-8">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="image">Image</SelectItem>
                    <SelectItem value="text">Text</SelectItem>
                    <SelectItem value="color">Color</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-1">
                <Label className="text-xs">Label</Label>
                <Input
                  value={newInputLabel}
                  onChange={(e) => setNewInputLabel(e.target.value)}
                  placeholder="e.g., Background, Accessory"
                  className="h-8"
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') handleAddInput();
                  }}
                />
              </div>
            </div>
            <div className="flex gap-2">
              <Button
                size="sm"
                onClick={handleAddInput}
                disabled={!newInputLabel.trim()}
                className="flex-1"
              >
                Add
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => {
                  setIsAddingInput(false);
                  setNewInputLabel('');
                }}
              >
                Cancel
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}