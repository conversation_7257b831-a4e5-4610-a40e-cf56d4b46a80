import React, { useState } from 'react';
import { ChevronDown, ChevronRight, Clock, Calendar, Hash, Eye, Download, Trash2, Check<PERSON><PERSON>cle, XCircle, Loader2 } from 'lucide-react';
import { Button } from '../ui/button';
import { cn } from '../common/utils/utils';
import { GeneratedImage } from './hooks/useGeneratedImages';
import { format } from 'date-fns';

interface BatchRowProps {
  batchId: string;
  prompt: string;
  images: GeneratedImage[];
  timestamp: Date;
  status?: string;
  totalImages?: number;
  completedImages?: number;
  metadata?: Record<string, any>;
  onImageClick: (image: GeneratedImage) => void;
  onImageSelect: (imageId: string) => void;
  selectedImages: Set<string>;
  onDownloadImage: (image: GeneratedImage) => void;
  onDeleteBatch?: (batchId: string) => void;
  index: number;
}

export function BatchRow({
  batchId,
  prompt,
  images,
  timestamp,
  status = 'completed',
  totalImages,
  completedImages,
  metadata,
  onImageClick,
  onImageSelect,
  selectedImages,
  onDownloadImage,
  onDeleteBatch,
  index
}: BatchRowProps) {
  const [isExpanded, setIsExpanded] = useState(true);
  const [hoveredImage, setHoveredImage] = useState<string | null>(null);

  const getStatusIcon = () => {
    switch (status) {
      case 'pending':
        return <Clock className="w-4 h-4 text-yellow-500" />;
      case 'generating':
        return <Loader2 className="w-4 h-4 text-blue-500 animate-spin" />;
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'partial':
        return <CheckCircle className="w-4 h-4 text-yellow-500" />;
      case 'failed':
        return <XCircle className="w-4 h-4 text-red-500" />;
      default:
        return null;
    }
  };

  const getStatusText = () => {
    if (status === 'generating' && totalImages) {
      return `Generating... ${completedImages || 0}/${totalImages}`;
    }
    if (status === 'partial' && totalImages) {
      return `Partial (${completedImages || 0}/${totalImages})`;
    }
    if (status === 'completed' && images.length > 0) {
      return `${images.length} images`;
    }
    return status;
  };

  return (
    <div className="bg-white rounded-lg border border-gray-200 overflow-hidden mb-4">
      {/* Batch Header */}
      <div className="px-4 py-3 border-b border-gray-200 bg-gray-50">
        <div className="flex items-start justify-between gap-4">
          <div className="flex items-start gap-3 flex-1 min-w-0">
            <button
              onClick={() => setIsExpanded(!isExpanded)}
              className="mt-1 text-gray-500 hover:text-gray-700 transition-colors"
            >
              {isExpanded ? <ChevronDown className="w-4 h-4" /> : <ChevronRight className="w-4 h-4" />}
            </button>
            
            <div className="flex-1 min-w-0">
              <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 mb-1">
                <div className="flex items-center gap-3">
                  <span className="text-sm font-medium text-gray-900">
                    Batch #{index + 1}
                  </span>
                  {getStatusIcon()}
                  <span className="text-sm text-gray-600">
                    {getStatusText()}
                  </span>
                </div>
                <div className="flex items-center gap-3 text-sm text-gray-500">
                  <span className="flex items-center gap-1">
                    <Calendar className="w-3 h-3" />
                    {format(timestamp, 'MMM d, yyyy')}
                  </span>
                  <span className="flex items-center gap-1">
                    <Clock className="w-3 h-3" />
                    {format(timestamp, 'h:mm a')}
                  </span>
                </div>
              </div>
              
              {metadata?.modelName && (
                <div className="flex items-center gap-4 text-xs text-gray-500 mt-1">
                  <span>Model: {metadata.modelName}</span>
                  {metadata.angle && <span>Angle: {metadata.angle}</span>}
                </div>
              )}
            </div>
          </div>

          {/* Batch Actions */}
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                // Download all images in batch
                images.forEach(img => onDownloadImage(img));
              }}
              className="text-gray-600 hover:text-gray-900"
            >
              <Download className="w-4 h-4" />
            </Button>
            {onDeleteBatch && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onDeleteBatch(batchId)}
                className="text-gray-600 hover:text-red-600"
              >
                <Trash2 className="w-4 h-4" />
              </Button>
            )}
          </div>
        </div>
      </div>

      {/* Images and Prompt */}
      {isExpanded && (
        <div className="flex flex-col lg:flex-row">
          {/* Images Section - Horizontal Scroll */}
          <div className="flex-1 p-4 overflow-x-auto min-w-0">
            <div className="flex gap-3" style={{ minHeight: '200px' }}>
              {images.length === 0 ? (
                <div className="flex items-center justify-center w-full text-gray-500">
                  {status === 'generating' ? (
                    <div className="text-center">
                      <Loader2 className="w-8 h-8 animate-spin mx-auto mb-2" />
                      <p className="text-sm">Generating images...</p>
                    </div>
                  ) : (
                    <p className="text-sm">No images in this batch</p>
                  )}
                </div>
              ) : (
                images.map((image, imgIndex) => (
                  <div
                    key={image.id}
                    className="flex-shrink-0 relative group"
                    onMouseEnter={() => setHoveredImage(image.id)}
                    onMouseLeave={() => setHoveredImage(null)}
                  >
                    {/* Selection Checkbox */}
                    <div className="absolute top-2 left-2 z-10">
                      <input
                        type="checkbox"
                        checked={selectedImages.has(image.id)}
                        onChange={() => onImageSelect(image.id)}
                        className="w-4 h-4 text-blue-600 bg-white border-gray-300 rounded focus:ring-blue-500 shadow-sm"
                        onClick={(e) => e.stopPropagation()}
                      />
                    </div>

                    {/* Image - 9:16 aspect ratio to match Fashion Lab output */}
                    <div
                      className={cn(
                        "cursor-pointer rounded-lg overflow-hidden border-2 transition-all aspect-[9/16] w-32 sm:w-36 md:w-40",
                        selectedImages.has(image.id)
                          ? "border-blue-500 shadow-lg"
                          : "border-gray-200 hover:border-gray-400 hover:shadow-md"
                      )}
                      onClick={() => onImageClick(image)}
                    >
                      <img
                        src={image.url}
                        alt={`Image ${imgIndex + 1}`}
                        className="w-full h-full object-cover"
                        loading="lazy"
                      />
                    </div>

                    {/* Hover Overlay */}
                    {hoveredImage === image.id && (
                      <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 rounded-lg flex items-end">
                        <div className="w-full p-2 bg-gradient-to-t from-black/80 to-transparent">
                          <div className="flex items-center justify-between text-white">
                            <span className="text-xs">
                              {image.metadata?.imageIndex && `#${image.metadata.imageIndex}`}
                            </span>
                            <div className="flex gap-1">
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  onImageClick(image);
                                }}
                                className="p-1 hover:bg-white/20 rounded"
                              >
                                <Eye className="w-3 h-3" />
                              </button>
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  onDownloadImage(image);
                                }}
                                className="p-1 hover:bg-white/20 rounded"
                              >
                                <Download className="w-3 h-3" />
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                ))
              )}
            </div>
          </div>

          {/* Prompt Section */}
          <div className="w-full lg:w-80 lg:flex-shrink-0 p-4 border-t lg:border-t-0 lg:border-l border-gray-200 bg-gray-50">
            <div className="mb-2">
              <h4 className="text-sm font-medium text-gray-700 mb-1">Prompt</h4>
              <p className="text-sm text-gray-600 leading-relaxed">
                {prompt || 'No prompt provided'}
              </p>
            </div>

            {/* Additional Metadata */}
            {metadata && Object.keys(metadata).length > 0 && (
              <div className="mt-4 pt-4 border-t border-gray-200">
                <h4 className="text-sm font-medium text-gray-700 mb-2">Details</h4>
                <div className="space-y-1">
                  {metadata.seeds && Array.isArray(metadata.seeds) && (
                    <div className="text-xs text-gray-600">
                      <span className="font-medium">Seeds:</span> {metadata.seeds.filter(s => s).join(', ') || 'Random'}
                    </div>
                  )}
                  {metadata.apiDelay && (
                    <div className="text-xs text-gray-600">
                      <span className="font-medium">Delay:</span> {metadata.apiDelay}ms
                    </div>
                  )}
                  {metadata.source && (
                    <div className="text-xs text-gray-600">
                      <span className="font-medium">Source:</span> {metadata.source}
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Batch ID */}
            <div className="mt-4 pt-4 border-t border-gray-200">
              <div className="flex items-center gap-1 text-xs text-gray-500">
                <Hash className="w-3 h-3" />
                <span className="font-mono truncate">{batchId}</span>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}