import React, { useState } from 'react';
import { Button } from '../ui/button';
import { AlertTriangle, Square, RefreshCw } from 'lucide-react';
import { useBackgroundQueue } from '../../contexts/BackgroundQueueContext';
import { backgroundQueueManager } from '../../services/backgroundQueueManager';
import { FashionLabImageService } from '../../services/fashionLabImageService';
import { toast } from 'react-hot-toast';

interface ForceStopButtonProps {
  className?: string;
}

export function ForceStopButton({ className = '' }: ForceStopButtonProps) {
  const { activeBatches } = useBackgroundQueue();
  const [isProcessing, setIsProcessing] = useState(false);
  const [isResuming, setIsResuming] = useState(false);

  const resumeStuckGenerations = async () => {
    if (activeBatches.length === 0) {
      toast.error('No active generations to resume');
      return;
    }

    setIsResuming(true);

    try {
      console.log('[ResumePolling] Starting resume of stuck generations');

      let resumedCount = 0;
      let completedCount = 0;
      const now = Date.now();
      const STUCK_THRESHOLD = 2 * 60 * 1000; // 2 minutes

      for (const batch of activeBatches) {
        const timeSinceCreated = now - new Date(batch.createdAt).getTime();
        const isStuck = timeSinceCreated > STUCK_THRESHOLD;

        console.log(`[ResumePolling] Batch ${batch.id}: ${timeSinceCreated}ms old, stuck: ${isStuck}`);

        if (isStuck && (batch.status === 'generating' || batch.status === 'pending')) {
          // Check each queue's actual status on the server
          for (const queue of batch.queues) {
            if (queue.status === 'generating' || queue.status === 'pending') {
              console.log(`[ResumePolling] Checking server status for queue ${queue.queueId}`);

              try {
                // Manually check the queue status on the server
                const serverStatus = await FashionLabImageService.checkQueueStatus(
                  queue.queueId,
                  batch.collectionId,
                  true, // store images if completed
                  batch.prompt,
                  batch.metadata
                );

                console.log(`[ResumePolling] Server status for ${queue.queueId}:`, serverStatus);

                if (serverStatus.status === 'completed') {
                  // Update the queue to completed status
                  queue.status = 'completed';
                  queue.progress = 100;
                  queue.completedAt = new Date();
                  queue.images = serverStatus.images || [];

                  // Update batch counters
                  batch.completedCount++;
                  completedCount++;

                  console.log(`[ResumePolling] Queue ${queue.queueId} was actually completed on server`);
                } else if (serverStatus.status === 'failed') {
                  // Mark as failed
                  queue.status = 'failed';
                  queue.error = serverStatus.error || 'Generation failed on server';
                  queue.completedAt = new Date();

                  batch.failedCount++;
                  console.log(`[ResumePolling] Queue ${queue.queueId} failed on server`);
                } else {
                  // Still processing - restart polling for this queue
                  queue.progress = serverStatus.progress || queue.progress;
                  (backgroundQueueManager as any).startQueuePolling(queue.queueId);
                  resumedCount++;
                  console.log(`[ResumePolling] Resumed polling for queue ${queue.queueId}`);
                }

              } catch (error) {
                console.error(`[ResumePolling] Error checking queue ${queue.queueId}:`, error);
                // If we can't check the status, assume it failed
                queue.status = 'failed';
                queue.error = 'Failed to check server status';
                queue.completedAt = new Date();
                batch.failedCount++;
              }
            }
          }

          // Update batch status after checking all queues
          (backgroundQueueManager as any).updateBatchStatus(batch);
        }
      }

      // Save changes and notify listeners
      (backgroundQueueManager as any).saveToStorage();
      (backgroundQueueManager as any).notifyListeners();

      if (completedCount > 0) {
        toast.success(`Found ${completedCount} completed generation${completedCount !== 1 ? 's' : ''} on server!`);
      }
      if (resumedCount > 0) {
        toast.success(`Resumed polling for ${resumedCount} generation${resumedCount !== 1 ? 's' : ''}`);
      }
      if (completedCount === 0 && resumedCount === 0) {
        toast.error('No stuck generations found to resume');
      }

    } catch (error) {
      console.error('[ResumePolling] Error during resume:', error);
      toast.error('Failed to resume stuck generations');
    } finally {
      setIsResuming(false);
    }
  };

  const forceStopAllStuckGenerations = async () => {
    if (activeBatches.length === 0) {
      toast.error('No active generations to stop');
      return;
    }

    setIsProcessing(true);

    try {
      console.log('[ForceStop] Starting force stop of all stuck generations');

      let stoppedCount = 0;
      const now = Date.now();
      const STUCK_THRESHOLD = 5 * 60 * 1000; // 5 minutes

      for (const batch of activeBatches) {
        const timeSinceCreated = now - new Date(batch.createdAt).getTime();
        const isStuck = timeSinceCreated > STUCK_THRESHOLD;

        console.log(`[ForceStop] Batch ${batch.id}: ${timeSinceCreated}ms old, stuck: ${isStuck}`);

        if (isStuck || batch.status === 'generating') {
          // Force stop all generating queues in this batch
          for (const queue of batch.queues) {
            if (queue.status === 'generating' || queue.status === 'pending') {
              console.log(`[ForceStop] Force stopping queue ${queue.queueId} in batch ${batch.id}`);

              // Stop polling for this queue
              (backgroundQueueManager as any).stopQueuePolling(queue.queueId);

              // Mark as failed with timeout error
              backgroundQueueManager.markQueueFailed(
                batch.id,
                queue.queueId,
                'Force stopped by user - generation timeout'
              );

              stoppedCount++;
            }
          }
        }
      }

      // Force restart global polling to clean up state
      (backgroundQueueManager as any).forceRestartPolling();

      if (stoppedCount > 0) {
        toast.success(`Force stopped ${stoppedCount} stuck generation${stoppedCount !== 1 ? 's' : ''}`);
      } else {
        toast.error('No stuck generations found to stop');
      }

    } catch (error) {
      console.error('[ForceStop] Error during force stop:', error);
      toast.error('Failed to force stop generations');
    } finally {
      setIsProcessing(false);
    }
  };

  const hasStuckGenerations = activeBatches.some(batch => {
    const timeSinceCreated = Date.now() - new Date(batch.createdAt).getTime();
    return timeSinceCreated > 2 * 60 * 1000; // 2+ minutes old
  });

  if (activeBatches.length === 0) {
    return null;
  }

  return (
    <div className={`flex gap-2 ${className}`}>
      <Button
        variant="outline"
        size="sm"
        onClick={resumeStuckGenerations}
        disabled={isResuming || isProcessing}
        className={`${hasStuckGenerations ? 'animate-pulse border-blue-500 text-blue-600' : ''}`}
      >
        <RefreshCw className={`w-4 h-4 mr-2 ${isResuming ? 'animate-spin' : ''}`} />
        {isResuming ? 'Checking...' : 'Resume Stuck'}
        {hasStuckGenerations && <AlertTriangle className="w-4 h-4 ml-2" />}
      </Button>

      <Button
        variant="destructive"
        size="sm"
        onClick={forceStopAllStuckGenerations}
        disabled={isProcessing || isResuming}
        className="opacity-75"
      >
        <Square className="w-4 h-4 mr-2" />
        {isProcessing ? 'Stopping...' : 'Force Stop'}
      </Button>
    </div>
  );
}
