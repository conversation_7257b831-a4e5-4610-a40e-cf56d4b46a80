import React, { useState, useEffect } from 'react';
import { useBackgroundQueue } from '../../contexts/BackgroundQueueContext';
import { FashionLabImageService } from '../../services/fashionLabImageService';
import { backgroundQueueManager } from '../../services/backgroundQueueManager';
import { Button } from '../ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { RefreshCw, Bug, Play, Square, Trash2 } from 'lucide-react';

interface QueueDebuggerProps {
  className?: string;
}

export function QueueDebugger({ className = '' }: QueueDebuggerProps) {
  const { batches, activeBatches } = useBackgroundQueue();
  const [debugInfo, setDebugInfo] = useState<any>(null);
  const [isChecking, setIsChecking] = useState(false);
  const [manualCheckResults, setManualCheckResults] = useState<Record<string, any>>({});

  // Get debug information
  const refreshDebugInfo = () => {
    const manager = backgroundQueueManager;
    const debugInfo = (manager as any).getDebugInfo();
    const info = {
      ...debugInfo,
      storageData: localStorage.getItem('fashionlab_generation_batches'),
    };
    setDebugInfo(info);
  };

  useEffect(() => {
    refreshDebugInfo();
  }, [batches, activeBatches]);

  // Manually check a queue status
  const manualCheckQueue = async (queueId: string, collectionId: string) => {
    setIsChecking(true);
    try {
      console.log(`[QueueDebugger] Manually checking queue ${queueId}`);
      const result = await FashionLabImageService.checkQueueStatus(
        queueId,
        collectionId,
        true
      );
      console.log(`[QueueDebugger] Queue ${queueId} result:`, result);
      
      setManualCheckResults(prev => ({
        ...prev,
        [queueId]: {
          ...result,
          checkedAt: new Date().toISOString()
        }
      }));
    } catch (error) {
      console.error(`[QueueDebugger] Error checking queue ${queueId}:`, error);
      setManualCheckResults(prev => ({
        ...prev,
        [queueId]: {
          error: error instanceof Error ? error.message : 'Unknown error',
          checkedAt: new Date().toISOString()
        }
      }));
    } finally {
      setIsChecking(false);
    }
  };

  // Force restart polling for a queue
  const restartQueuePolling = (queueId: string) => {
    console.log(`[QueueDebugger] Restarting polling for queue ${queueId}`);
    // Access private method through any cast
    (backgroundQueueManager as any).stopQueuePolling(queueId);
    (backgroundQueueManager as any).startQueuePolling(queueId);
  };

  // Force restart global polling
  const restartGlobalPolling = () => {
    console.log(`[QueueDebugger] Restarting global polling`);
    (backgroundQueueManager as any).forceRestartPolling();
    setTimeout(refreshDebugInfo, 1500); // Refresh after restart
  };

  // Clear stuck batches
  const clearStuckBatches = () => {
    console.log(`[QueueDebugger] Clearing stuck batches`);
    const stuckBatches = activeBatches.filter(batch => {
      const timeSinceCreated = Date.now() - new Date(batch.createdAt).getTime();
      return timeSinceCreated > 10 * 60 * 1000; // 10 minutes
    });
    
    stuckBatches.forEach(batch => {
      batch.queues.forEach(queue => {
        if (queue.status === 'generating') {
          backgroundQueueManager.markQueueFailed(batch.id, queue.queueId, 'Manually cleared as stuck');
        }
      });
    });
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Bug className="w-5 h-5" />
          Queue Debugger
          <Button
            variant="outline"
            size="sm"
            onClick={refreshDebugInfo}
            className="ml-auto"
          >
            <RefreshCw className="w-4 h-4" />
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Debug Info */}
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <strong>Total Batches:</strong> {debugInfo?.totalBatches || 0}
          </div>
          <div>
            <strong>Active Batches:</strong> {debugInfo?.activeBatches || 0}
          </div>
          <div>
            <strong>Total Queues:</strong> {debugInfo?.totalQueues || 0}
          </div>
          <div>
            <strong>Active Queues:</strong> {debugInfo?.activeQueues || 0}
          </div>
          <div>
            <strong>Polling Intervals:</strong> {debugInfo?.pollingIntervals || 0}
          </div>
          <div>
            <strong>Global Polling:</strong> {debugInfo?.isPolling ? 'Active' : 'Inactive'}
          </div>
        </div>

        {/* Global Actions */}
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={restartGlobalPolling}
          >
            <Play className="w-4 h-4 mr-1" />
            Restart Global Polling
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={clearStuckBatches}
            className="text-red-600 hover:text-red-700"
          >
            <Trash2 className="w-4 h-4 mr-1" />
            Clear Stuck Batches
          </Button>
        </div>

        {/* Active Batches Debug */}
        {activeBatches.length > 0 && (
          <div className="space-y-3">
            <h4 className="font-medium">Active Batches:</h4>
            {activeBatches.map(batch => (
              <div key={batch.id} className="border rounded p-3 space-y-2">
                <div className="flex items-center justify-between">
                  <div>
                    <Badge variant="outline">{batch.status}</Badge>
                    <span className="ml-2 text-sm">
                      {batch.completedCount}/{batch.totalCount} completed
                    </span>
                  </div>
                  <div className="text-xs text-gray-500">
                    Created: {new Date(batch.createdAt).toLocaleTimeString()}
                  </div>
                </div>
                
                <div className="text-sm text-gray-600 truncate">
                  {batch.prompt}
                </div>

                {/* Queue Items */}
                <div className="space-y-1">
                  {batch.queues.map(queue => (
                    <div key={queue.queueId} className="flex items-center justify-between text-xs bg-gray-50 p-2 rounded">
                      <div className="flex items-center gap-2">
                        <Badge variant={queue.status === 'generating' ? 'default' : queue.status === 'completed' ? 'secondary' : 'destructive'}>
                          {queue.status}
                        </Badge>
                        <span>Queue #{queue.imageIndex}</span>
                        <span className="text-gray-500">{queue.queueId.substring(0, 8)}...</span>
                      </div>
                      
                      <div className="flex items-center gap-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => manualCheckQueue(queue.queueId, batch.collectionId)}
                          disabled={isChecking}
                          className="h-6 px-2"
                        >
                          Check
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => restartQueuePolling(queue.queueId)}
                          className="h-6 px-2"
                        >
                          Restart
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Manual Check Results */}
                {batch.queues.some(q => manualCheckResults[q.queueId]) && (
                  <div className="mt-2 space-y-1">
                    <h5 className="text-xs font-medium">Manual Check Results:</h5>
                    {batch.queues.map(queue => {
                      const result = manualCheckResults[queue.queueId];
                      if (!result) return null;
                      
                      return (
                        <div key={queue.queueId} className="text-xs bg-blue-50 p-2 rounded">
                          <div className="font-medium">Queue {queue.imageIndex}:</div>
                          {result.error ? (
                            <div className="text-red-600">Error: {result.error}</div>
                          ) : (
                            <div>
                              Status: {result.status}, Progress: {result.progress}%
                              {result.images && <div>Images: {result.images.length}</div>}
                            </div>
                          )}
                          <div className="text-gray-500">Checked: {new Date(result.checkedAt).toLocaleTimeString()}</div>
                        </div>
                      );
                    })}
                  </div>
                )}
              </div>
            ))}
          </div>
        )}

        {/* Storage Debug */}
        {debugInfo?.storageData && (
          <details className="text-xs">
            <summary className="cursor-pointer font-medium">Storage Data</summary>
            <pre className="mt-2 bg-gray-100 p-2 rounded overflow-auto max-h-32">
              {JSON.stringify(JSON.parse(debugInfo.storageData), null, 2)}
            </pre>
          </details>
        )}
      </CardContent>
    </Card>
  );
}
