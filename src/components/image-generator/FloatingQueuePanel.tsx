import React, { useState } from 'react';
import { useBackgroundQueue, useActiveGenerations } from '../../contexts/BackgroundQueueContext';
import { 
  Activity, X, ChevronLeft, ChevronRight, Clock, CheckCircle, 
  XCircle, Loader2, Trash2, Eye, <PERSON>Off, Minimize2, Maximize2 
} from 'lucide-react';
import { Button } from '../ui/button';
import { cn } from '../common/utils/utils';
import { ForceStopButton } from './ForceStopButton';

interface FloatingQueuePanelProps {
  collectionId?: string;
}

export function FloatingQueuePanel({ collectionId }: FloatingQueuePanelProps) {
  const { batches, activeBatches, cleanup } = useBackgroundQueue();
  const { hasActiveGenerations, totalActiveImages, totalCompletedImages } = useActiveGenerations();
  const [isOpen, setIsOpen] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);
  const [showCompleted, setShowCompleted] = useState(false);
  const [expandedBatches, setExpandedBatches] = useState<Set<string>>(new Set());

  // Filter batches by collection if specified
  const filteredBatches = collectionId 
    ? batches.filter(batch => batch.collectionId === collectionId)
    : batches;

  const activeBatchesFiltered = collectionId
    ? activeBatches.filter(batch => batch.collectionId === collectionId)
    : activeBatches;

  const completedBatches = filteredBatches.filter(batch => 
    batch.status === 'completed' || batch.status === 'failed' || batch.status === 'partial'
  );

  const toggleBatchExpansion = (batchId: string) => {
    const newExpanded = new Set(expandedBatches);
    if (newExpanded.has(batchId)) {
      newExpanded.delete(batchId);
    } else {
      newExpanded.add(batchId);
    }
    setExpandedBatches(newExpanded);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="w-4 h-4 text-yellow-500" />;
      case 'generating':
        return <Loader2 className="w-4 h-4 text-blue-500 animate-spin" />;
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'partial':
        return <CheckCircle className="w-4 h-4 text-yellow-500" />;
      case 'failed':
        return <XCircle className="w-4 h-4 text-red-500" />;
      default:
        return null;
    }
  };

  // Floating indicator when panel is closed
  if (!isOpen && filteredBatches.length > 0) {
    return (
      <div className="fixed bottom-6 right-6 z-50">
        <button
          onClick={() => setIsOpen(true)}
          className={cn(
            "bg-white rounded-full shadow-lg border border-gray-200 px-4 py-3",
            "hover:shadow-xl transition-all duration-200",
            "flex items-center gap-3"
          )}
        >
          <Activity className={cn(
            "w-5 h-5",
            activeBatchesFiltered.length > 0 ? "text-blue-500 animate-pulse" : "text-gray-600"
          )} />
          <div className="text-sm">
            {activeBatchesFiltered.length > 0 ? (
              <span className="font-medium">
                {activeBatchesFiltered.length} active
              </span>
            ) : (
              <span className="text-gray-600">
                {completedBatches.length} completed
              </span>
            )}
          </div>
          {activeBatchesFiltered.length > 0 && (
            <div className="flex items-center gap-1">
              <div className="w-16 h-1.5 bg-gray-200 rounded-full overflow-hidden">
                <div
                  className="h-full bg-blue-500 transition-all duration-300"
                  style={{ 
                    width: `${(totalCompletedImages / (totalActiveImages || 1)) * 100}%` 
                  }}
                />
              </div>
              <span className="text-xs text-gray-500">
                {totalCompletedImages}/{totalActiveImages}
              </span>
            </div>
          )}
        </button>
      </div>
    );
  }

  // Floating panel
  if (isOpen) {
    return (
      <div className={cn(
        "fixed right-0 top-0 h-full z-50",
        "bg-white shadow-2xl border-l border-gray-200",
        "transition-all duration-300 ease-in-out",
        isMinimized ? "w-16" : "w-96"
      )}>
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          {!isMinimized && (
            <div className="flex items-center gap-2">
              <Activity className="w-5 h-5 text-gray-600" />
              <h3 className="font-semibold text-gray-900">Generation Queue</h3>
            </div>
          )}
          
          <div className="flex items-center gap-1">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setIsMinimized(!isMinimized)}
              className="h-8 w-8"
            >
              {isMinimized ? <ChevronLeft className="w-4 h-4" /> : <ChevronRight className="w-4 h-4" />}
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setIsOpen(false)}
              className="h-8 w-8"
            >
              <X className="w-4 h-4" />
            </Button>
          </div>
        </div>

        {/* Content */}
        {!isMinimized && (
          <div className="h-full overflow-y-auto pb-20">
            {/* Summary */}
            <div className="p-4 bg-gray-50 border-b border-gray-200">
              <div className="space-y-2">
                {activeBatchesFiltered.length > 0 && (
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Active Generations</span>
                    <span className="text-sm font-medium">{activeBatchesFiltered.length}</span>
                  </div>
                )}
                {completedBatches.length > 0 && (
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Completed</span>
                    <span className="text-sm font-medium">{completedBatches.length}</span>
                  </div>
                )}
                {totalActiveImages > 0 && (
                  <div className="space-y-1">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-600">Overall Progress</span>
                      <span className="font-medium">{totalCompletedImages}/{totalActiveImages}</span>
                    </div>
                    <div className="w-full h-2 bg-gray-200 rounded-full overflow-hidden">
                      <div
                        className="h-full bg-blue-500 transition-all duration-300"
                        style={{ 
                          width: `${(totalCompletedImages / totalActiveImages) * 100}%` 
                        }}
                      />
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Batch List */}
            <div className="p-4 space-y-3">
              {/* Active Batches */}
              {activeBatchesFiltered.map((batch, index) => {
                const isExpanded = expandedBatches.has(batch.id);
                const progress = (batch.completedCount / batch.totalCount) * 100;
                
                return (
                  <div
                    key={batch.id}
                    className="bg-white rounded-lg border border-gray-200 p-3 space-y-2"
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex items-start gap-2 flex-1">
                        {getStatusIcon(batch.status)}
                        <div className="flex-1">
                          <div className="flex items-center gap-2">
                            <span className="text-sm font-medium">Batch {index + 1}</span>
                            <span className="text-xs text-gray-500">
                              {batch.completedCount}/{batch.totalCount} images
                            </span>
                          </div>
                          {isExpanded && (
                            <p className="text-xs text-gray-600 mt-1 line-clamp-2">
                              {batch.prompt}
                            </p>
                          )}
                        </div>
                      </div>
                      <button
                        onClick={() => toggleBatchExpansion(batch.id)}
                        className="text-gray-400 hover:text-gray-600"
                      >
                        {isExpanded ? <ChevronLeft className="w-4 h-4" /> : <ChevronLeft className="w-4 h-4 rotate-180" />}
                      </button>
                    </div>
                    
                    <div className="w-full h-1.5 bg-gray-200 rounded-full overflow-hidden">
                      <div
                        className="h-full bg-blue-500 transition-all duration-300"
                        style={{ width: `${progress}%` }}
                      />
                    </div>

                    {batch.status === 'generating' && batch.queues.length > 0 && (
                      <ForceStopButton batchId={batch.id} size="sm" />
                    )}
                  </div>
                );
              })}

              {/* Completed Batches (if showing) */}
              {showCompleted && completedBatches.length > 0 && (
                <>
                  <div className="text-xs text-gray-500 font-medium pt-2">Completed</div>
                  {completedBatches.map((batch, index) => (
                    <div
                      key={batch.id}
                      className="bg-gray-50 rounded-lg border border-gray-200 p-3"
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          {getStatusIcon(batch.status)}
                          <span className="text-sm text-gray-600">Batch {index + 1}</span>
                          <span className="text-xs text-gray-500">
                            {batch.completedCount} images
                          </span>
                        </div>
                        <button
                          onClick={() => {
                            // Remove batch from manager
                            cleanup();
                          }}
                          className="text-gray-400 hover:text-red-500"
                        >
                          <Trash2 className="w-3 h-3" />
                        </button>
                      </div>
                    </div>
                  ))}
                </>
              )}

              {/* Toggle completed visibility */}
              {completedBatches.length > 0 && (
                <button
                  onClick={() => setShowCompleted(!showCompleted)}
                  className="text-xs text-gray-500 hover:text-gray-700 flex items-center gap-1"
                >
                  {showCompleted ? <EyeOff className="w-3 h-3" /> : <Eye className="w-3 h-3" />}
                  {showCompleted ? 'Hide' : 'Show'} completed ({completedBatches.length})
                </button>
              )}
            </div>
          </div>
        )}

        {/* Minimized state */}
        {isMinimized && (
          <div className="p-2 space-y-2">
            {activeBatchesFiltered.map((batch) => {
              const progress = (batch.completedCount / batch.totalCount) * 100;
              return (
                <div
                  key={batch.id}
                  className="w-10 h-10 rounded-lg bg-gray-100 flex items-center justify-center relative"
                  title={`Batch: ${batch.completedCount}/${batch.totalCount} images`}
                >
                  <div className="absolute inset-0 rounded-lg overflow-hidden">
                    <div
                      className="absolute bottom-0 left-0 right-0 bg-blue-500 transition-all duration-300"
                      style={{ height: `${progress}%` }}
                    />
                  </div>
                  <span className="text-xs font-medium relative z-10">
                    {Math.round(progress)}%
                  </span>
                </div>
              );
            })}
          </div>
        )}
      </div>
    );
  }

  return null;
}