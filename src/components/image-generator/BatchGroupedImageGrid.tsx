import React, { useMemo, useState } from 'react';
import { GeneratedImage } from './hooks/useGeneratedImages';
import { useBackgroundQueue } from '../../contexts/BackgroundQueueContext';
import { Loader2, LayoutGrid, LayoutList } from 'lucide-react';
import { Button } from '../ui/button';
import { BatchRow } from './BatchRow';
import { cn } from '../common/utils/utils';

interface BatchGroupedImageGridProps {
  images: GeneratedImage[];
  selectedImages: Set<string>;
  onToggleSelection: (imageId: string) => void;
  onViewImage: (image: GeneratedImage) => void;
  onDownloadImage: (image: GeneratedImage) => void;
  isLoading: boolean;
  className?: string;
}

interface BatchGroup {
  batchId: string;
  batchTableId?: string;
  prompt: string;
  createdAt: Date;
  images: GeneratedImage[];
  status?: string;
  completedCount?: number;
  totalCount?: number;
  metadata?: Record<string, any>;
}

export function BatchGroupedImageGrid({
  images,
  selectedImages,
  onToggleSelection,
  onViewImage,
  onDownloadImage,
  isLoading,
  className = ''
}: BatchGroupedImageGridProps) {
  const { batches } = useBackgroundQueue();
  const [viewMode, setViewMode] = useState<'list' | 'grid'>('list');

  // Group images by batch
  const batchGroups = useMemo(() => {
    const groups: Record<string, BatchGroup> = {};

    // First, create groups from database batch info
    images.forEach(image => {
      // Try to get batch info from metadata
      const batchInfo = image.metadata?.batch;
      const batchId = image.metadata?.batchId || image.metadata?.batch_id;
      const batchTableId = image.metadata?.batchTableId;
      
      // Determine the group key
      let groupKey = 'other';
      if (batchTableId) {
        groupKey = batchTableId;
      } else if (batchId) {
        groupKey = batchId;
      }
      
      if (!groups[groupKey]) {
        groups[groupKey] = {
          batchId: groupKey,
          batchTableId: batchTableId,
          prompt: batchInfo?.prompt || image.prompt || 'Individual generation',
          createdAt: batchInfo?.created_at ? new Date(batchInfo.created_at) : image.timestamp,
          images: [],
          status: batchInfo?.status,
          completedCount: batchInfo?.completed_images,
          totalCount: batchInfo?.total_images,
          metadata: batchInfo?.metadata || image.metadata,
        };
      }
      
      groups[groupKey].images.push(image);
    });

    // Also add active batches from BackgroundQueueContext that may not have images yet
    batches.forEach(batch => {
      if (!groups[batch.id]) {
        groups[batch.id] = {
          batchId: batch.id,
          prompt: batch.prompt,
          createdAt: batch.createdAt,
          images: [],
          status: batch.status,
          completedCount: batch.completedCount,
          totalCount: batch.totalCount,
          metadata: batch.metadata,
        };
      } else {
        // Update status from active batch if it exists
        groups[batch.id].status = batch.status;
        groups[batch.id].completedCount = batch.completedCount;
        groups[batch.id].totalCount = batch.totalCount;
      }
    });

    // Convert to array and sort by creation date (newest first)
    return Object.values(groups)
      .filter(group => group.images.length > 0 || group.status === 'generating' || group.status === 'pending')
      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
  }, [images, batches]);

  if (isLoading) {
    return (
      <div className={cn("flex items-center justify-center py-12", className)}>
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-gray-400" />
          <p className="text-gray-500">Loading images...</p>
        </div>
      </div>
    );
  }

  if (batchGroups.length === 0) {
    return (
      <div className={cn("text-center py-12", className)}>
        <div className="text-gray-500">
          <p className="text-lg mb-2">No images generated yet</p>
          <p className="text-sm">Start generating images to see them here</p>
        </div>
      </div>
    );
  }

  // List View (Midjourney-style)
  if (viewMode === 'list') {
    return (
      <div className={cn("space-y-2", className)}>
        {/* View Mode Toggle */}
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold">Generation History</h3>
          <div className="flex items-center gap-2">
            <Button
              variant={viewMode === 'list' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('list')}
              className="flex items-center gap-1"
            >
              <LayoutList className="w-4 h-4" />
              List
            </Button>
            <Button
              variant={viewMode === 'grid' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('grid')}
              className="flex items-center gap-1"
            >
              <LayoutGrid className="w-4 h-4" />
              Grid
            </Button>
          </div>
        </div>

        {/* Batch Rows */}
        {batchGroups.map((group, index) => (
          <BatchRow
            key={group.batchId}
            batchId={group.batchId}
            prompt={group.prompt}
            images={group.images}
            timestamp={group.createdAt}
            status={group.status}
            totalImages={group.totalCount}
            completedImages={group.completedCount}
            metadata={group.metadata}
            onImageClick={onViewImage}
            onImageSelect={onToggleSelection}
            selectedImages={selectedImages}
            onDownloadImage={onDownloadImage}
            index={index}
          />
        ))}
      </div>
    );
  }

  // Grid View (Traditional)
  return (
    <div className={cn("space-y-4", className)}>
      {/* View Mode Toggle */}
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-semibold">Generation History</h3>
        <div className="flex items-center gap-2">
          <Button
            variant={viewMode === 'list' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setViewMode('list')}
            className="flex items-center gap-1"
          >
            <LayoutList className="w-4 h-4" />
            List
          </Button>
          <Button
            variant={viewMode === 'grid' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setViewMode('grid')}
            className="flex items-center gap-1"
          >
            <LayoutGrid className="w-4 h-4" />
            Grid
          </Button>
        </div>
      </div>

      {/* Traditional Grid Layout */}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
        {images.map((image) => (
          <div
            key={image.id}
            className={cn(
              "relative group cursor-pointer rounded-lg overflow-hidden border-2 transition-all duration-200 bg-white",
              selectedImages.has(image.id)
                ? "border-blue-500 ring-2 ring-blue-200 shadow-lg"
                : "border-gray-200 hover:border-gray-400 hover:shadow-md"
            )}
            onClick={() => onToggleSelection(image.id)}
          >
            {/* Selection Checkbox */}
            <div className="absolute top-2 left-2 z-10">
              <input
                type="checkbox"
                checked={selectedImages.has(image.id)}
                onChange={() => onToggleSelection(image.id)}
                className="w-4 h-4 text-blue-600 bg-white border-gray-300 rounded focus:ring-blue-500 shadow-sm"
                onClick={(e) => e.stopPropagation()}
              />
            </div>

            {/* Image */}
            <div className="aspect-[4/5] bg-gray-50 flex items-center justify-center">
              <img
                src={image.url}
                alt={`Generated image ${image.id}`}
                className="w-full h-full object-cover"
                loading="lazy"
              />
            </div>

            {/* Overlay Actions */}
            <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 flex items-center justify-center">
              <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex gap-2">
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    onViewImage(image);
                  }}
                  className="px-3 py-1 bg-white text-gray-900 rounded text-sm font-medium hover:bg-gray-100"
                >
                  View
                </button>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    onDownloadImage(image);
                  }}
                  className="px-3 py-1 bg-white text-gray-900 rounded text-sm font-medium hover:bg-gray-100"
                >
                  Download
                </button>
              </div>
            </div>

            {/* Image Info */}
            <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-2">
              <div className="text-white text-xs">
                {image.timestamp && (
                  <div className="opacity-90">
                    {new Date(image.timestamp).toLocaleTimeString()}
                  </div>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}