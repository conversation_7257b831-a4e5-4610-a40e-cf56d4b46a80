import React, { useState } from 'react';
import { useBackgroundQueue } from '../../contexts/BackgroundQueueContext';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { AlertTriangle, Trash2, RefreshCw, Database } from 'lucide-react';
import { toast } from 'react-hot-toast';

interface BatchManagerProps {
  className?: string;
}

export function BatchManager({ className = '' }: BatchManagerProps) {
  const { 
    batches, 
    forceRemoveBatch, 
    forceCleanAll, 
    cleanupOrphanedBatches 
  } = useBackgroundQueue();
  
  const [batchIdToRemove, setBatchIdToRemove] = useState('');
  const [isCleaningOrphaned, setIsCleaningOrphaned] = useState(false);

  const handleRemoveSpecificBatch = () => {
    if (!batchIdToRemove.trim()) {
      toast.error('Please enter a batch ID');
      return;
    }

    const success = forceRemoveBatch(batchIdToRemove.trim());
    if (success) {
      toast.success(`Batch ${batchIdToRemove} removed successfully`);
      setBatchIdToRemove('');
    } else {
      toast.error(`Batch ${batchIdToRemove} not found`);
    }
  };

  const handleCleanupOrphaned = async () => {
    setIsCleaningOrphaned(true);
    try {
      await cleanupOrphanedBatches();
      toast.success('Orphaned batches cleaned up successfully');
    } catch (error) {
      toast.error('Failed to cleanup orphaned batches');
      console.error('Cleanup error:', error);
    } finally {
      setIsCleaningOrphaned(false);
    }
  };

  const handleForceCleanAll = () => {
    if (window.confirm('Are you sure you want to remove ALL batches? This cannot be undone.')) {
      forceCleanAll();
      toast.success('All batches cleared');
    }
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Database className="h-5 w-5" />
          Batch Manager
        </CardTitle>
        <CardDescription>
          Admin tools for managing AI generation batches. Use with caution.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Current Batches */}
        <div>
          <h3 className="text-sm font-medium mb-2">Current Batches ({batches.length})</h3>
          {batches.length === 0 ? (
            <p className="text-sm text-muted-foreground">No batches found</p>
          ) : (
            <div className="space-y-2 max-h-40 overflow-y-auto">
              {batches.map((batch) => (
                <div 
                  key={batch.id} 
                  className="flex items-center justify-between p-2 bg-muted rounded text-sm"
                >
                  <div>
                    <span className="font-mono">{batch.id}</span>
                    <span className="ml-2 text-muted-foreground">({batch.status})</span>
                  </div>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => {
                      if (forceRemoveBatch(batch.id)) {
                        toast.success(`Removed batch ${batch.id}`);
                      }
                    }}
                  >
                    <Trash2 className="h-3 w-3" />
                  </Button>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Remove Specific Batch */}
        <div>
          <h3 className="text-sm font-medium mb-2">Remove Specific Batch</h3>
          <div className="flex gap-2">
            <Input
              placeholder="Enter batch ID (e.g., batch_1754648763111_y1tzfp40z)"
              value={batchIdToRemove}
              onChange={(e) => setBatchIdToRemove(e.target.value)}
              className="font-mono text-sm"
            />
            <Button 
              onClick={handleRemoveSpecificBatch}
              variant="outline"
              size="sm"
            >
              <Trash2 className="h-4 w-4 mr-1" />
              Remove
            </Button>
          </div>
        </div>

        {/* Cleanup Actions */}
        <div className="space-y-3">
          <h3 className="text-sm font-medium">Cleanup Actions</h3>
          
          <Button
            onClick={handleCleanupOrphaned}
            disabled={isCleaningOrphaned}
            variant="outline"
            className="w-full"
          >
            {isCleaningOrphaned ? (
              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <Database className="h-4 w-4 mr-2" />
            )}
            Cleanup Orphaned Batches
          </Button>
          
          <Button
            onClick={handleForceCleanAll}
            variant="destructive"
            className="w-full"
          >
            <AlertTriangle className="h-4 w-4 mr-2" />
            Clear All Batches
          </Button>
        </div>

        {/* Help Text */}
        <div className="text-xs text-muted-foreground space-y-1">
          <p><strong>Cleanup Orphaned:</strong> Removes batches that exist in localStorage but not in the database</p>
          <p><strong>Clear All:</strong> Removes all batches from memory and localStorage (nuclear option)</p>
        </div>
      </CardContent>
    </Card>
  );
}
