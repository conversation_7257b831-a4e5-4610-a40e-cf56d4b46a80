/**
 * Test utility to verify batch cleanup functionality
 * This can be run in the browser console to test the new cleanup features
 */

import { backgroundQueueManager } from '../services/backgroundQueueManager';

export const batchCleanupTest = {
  // Test removing the specific hanging batch
  removeHangingBatch: () => {
    const batchId = 'batch_1754648763111_y1tzfp40z';
    console.log(`Attempting to remove hanging batch: ${batchId}`);
    
    const success = backgroundQueueManager.forceRemoveBatch(batchId);
    if (success) {
      console.log(`✅ Successfully removed batch: ${batchId}`);
    } else {
      console.log(`❌ Batch not found: ${batchId}`);
    }
    
    return success;
  },

  // Test cleanup of orphaned batches
  cleanupOrphaned: async () => {
    console.log('Starting orphaned batch cleanup...');
    try {
      await backgroundQueueManager.cleanupOrphanedBatches();
      console.log('✅ Orphaned batch cleanup completed');
    } catch (error) {
      console.error('❌ Orphaned batch cleanup failed:', error);
    }
  },

  // Test force clear all (use with caution)
  forceCleanAll: () => {
    console.warn('⚠️ Force clearing ALL batches...');
    backgroundQueueManager.forceCleanAll();
    console.log('✅ All batches cleared');
  },

  // Get current batch status
  getBatchStatus: () => {
    const batches = backgroundQueueManager.getBatches();
    console.log(`Current batches: ${batches.length}`);
    batches.forEach(batch => {
      console.log(`- ${batch.id} (${batch.status}) - ${batch.prompt.substring(0, 50)}...`);
    });
    return batches;
  },

  // Check localStorage
  checkStorage: () => {
    const stored = localStorage.getItem('fashionlab_generation_batches');
    if (stored) {
      const data = JSON.parse(stored);
      console.log('localStorage data:', data);
      return data;
    } else {
      console.log('No localStorage data found');
      return null;
    }
  }
};

// Make it available globally for console testing
if (typeof window !== 'undefined') {
  (window as any).batchCleanupTest = batchCleanupTest;
}
