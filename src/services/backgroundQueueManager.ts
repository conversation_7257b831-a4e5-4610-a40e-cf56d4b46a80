import { FashionLabImageService } from './fashionLabImageService';
import { toast } from 'react-hot-toast';

export interface GenerationBatch {
  id: string;
  collectionId: string;
  prompt: string;
  metadata?: Record<string, unknown>;
  createdAt: Date;
  status: 'pending' | 'generating' | 'completed' | 'failed' | 'partial';
  queues: QueueItem[];
  completedCount: number;
  failedCount: number;
  totalCount: number;
  settings: {
    numImages: number;
    apiDelay: number;
    seeds?: number[];
  };
}

export interface QueueItem {
  queueId: string;
  batchId: string;
  imageIndex: number;
  status: 'pending' | 'generating' | 'completed' | 'failed';
  progress: number;
  error?: string;
  createdAt: Date;
  completedAt?: Date;
}

export interface BatchProgress {
  batchId: string;
  overallProgress: number;
  status: string;
  completedCount: number;
  failedCount: number;
  totalCount: number;
}

class BackgroundQueueManager {
  private static instance: BackgroundQueueManager;
  private batches: Map<string, GenerationBatch> = new Map();
  private queues: Map<string, QueueItem> = new Map();
  private pollingIntervals: Map<string, NodeJS.Timeout> = new Map();
  private listeners: Set<(batches: GenerationBatch[]) => void> = new Set();
  private isPolling = false;
  private readonly STORAGE_KEY = 'fashionlab_generation_batches';
  private readonly POLL_INTERVAL = 5000; // 5 seconds
  private readonly MAX_POLL_ATTEMPTS = 120; // 10 minutes max

  private constructor() {
    this.loadFromStorage();
    this.startGlobalPolling();
    
    // Listen for page visibility changes to resume polling
    document.addEventListener('visibilitychange', () => {
      if (!document.hidden && !this.isPolling) {
        this.startGlobalPolling();
      }
    });

    // Save state before page unload
    window.addEventListener('beforeunload', () => {
      this.saveToStorage();
    });
  }

  static getInstance(): BackgroundQueueManager {
    if (!BackgroundQueueManager.instance) {
      BackgroundQueueManager.instance = new BackgroundQueueManager();
    }
    return BackgroundQueueManager.instance;
  }

  // Create a new generation batch
  createBatch(params: {
    collectionId: string;
    prompt: string;
    metadata?: Record<string, unknown>;
    numImages: number;
    apiDelay: number;
    seeds?: number[];
  }): string {
    const batchId = `batch_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const batch: GenerationBatch = {
      id: batchId,
      collectionId: params.collectionId,
      prompt: params.prompt,
      metadata: params.metadata,
      createdAt: new Date(),
      status: 'pending',
      queues: [],
      completedCount: 0,
      failedCount: 0,
      totalCount: params.numImages,
      settings: {
        numImages: params.numImages,
        apiDelay: params.apiDelay,
        seeds: params.seeds,
      },
    };

    this.batches.set(batchId, batch);
    this.saveToStorage();
    this.notifyListeners();
    
    console.log(`[BackgroundQueueManager] Created batch ${batchId} for ${params.numImages} images`);
    return batchId;
  }

  // Add queue to batch
  addQueueToBatch(batchId: string, queueId: string, imageIndex: number): void {
    const batch = this.batches.get(batchId);
    if (!batch) {
      console.error(`[BackgroundQueueManager] Batch ${batchId} not found`);
      return;
    }

    const queueItem: QueueItem = {
      queueId,
      batchId,
      imageIndex,
      status: 'generating',
      progress: 0,
      createdAt: new Date(),
    };

    batch.queues.push(queueItem);
    batch.status = 'generating';
    this.queues.set(queueId, queueItem);
    
    this.saveToStorage();
    this.notifyListeners();
    
    // Start polling for this specific queue
    this.startQueuePolling(queueId);
    
    console.log(`[BackgroundQueueManager] Added queue ${queueId} to batch ${batchId}`);
  }

  // Mark queue as failed
  markQueueFailed(batchId: string, queueId: string, error: string): void {
    const batch = this.batches.get(batchId);
    const queue = this.queues.get(queueId);
    
    if (batch && queue) {
      queue.status = 'failed';
      queue.error = error;
      queue.completedAt = new Date();
      
      batch.failedCount++;
      this.updateBatchStatus(batch);
      
      this.saveToStorage();
      this.notifyListeners();
      
      console.log(`[BackgroundQueueManager] Marked queue ${queueId} as failed: ${error}`);
    }
  }

  // Start polling for a specific queue
  startQueuePolling(queueId: string): void {
    if (this.pollingIntervals.has(queueId)) {
      console.log(`[BackgroundQueueManager] Queue ${queueId} already being polled`);
      return; // Already polling
    }

    console.log(`[BackgroundQueueManager] Starting polling for queue ${queueId}`);
    let attempts = 0;
    const poll = async () => {
      try {
        const queue = this.queues.get(queueId);
        const batch = queue ? this.batches.get(queue.batchId) : null;

        if (!queue || !batch) {
          console.log(`[BackgroundQueueManager] Queue ${queueId} or batch not found, stopping polling`);
          this.stopQueuePolling(queueId);
          return;
        }

        if (queue.status === 'completed' || queue.status === 'failed') {
          console.log(`[BackgroundQueueManager] Queue ${queueId} finished with status ${queue.status}, stopping polling`);
          this.stopQueuePolling(queueId);
          return;
        }

        attempts++;
        console.log(`[BackgroundQueueManager] Polling queue ${queueId}, attempt ${attempts}/${this.MAX_POLL_ATTEMPTS}`);

        if (attempts > this.MAX_POLL_ATTEMPTS) {
          console.log(`[BackgroundQueueManager] Queue ${queueId} polling timeout after ${attempts} attempts`);
          this.markQueueFailed(batch.id, queueId, 'Polling timeout');
          this.stopQueuePolling(queueId);
          return;
        }

        const result = await FashionLabImageService.checkQueueStatus(
          queueId,
          batch.collectionId,
          true,
          batch.prompt,
          batch.metadata
        );

        console.log(`[BackgroundQueueManager] Queue ${queueId} status: ${result.status}, progress: ${result.progress}%`);

        // Update queue progress
        queue.progress = result.progress;
        
        if (result.status === 'completed' && result.stored) {
          queue.status = 'completed';
          queue.progress = 100;
          queue.completedAt = new Date();
          batch.completedCount++;
          
          this.updateBatchStatus(batch);
          this.stopQueuePolling(queueId);
          
          console.log(`[BackgroundQueueManager] Queue ${queueId} completed`);
        } else if (result.status === 'failed') {
          this.markQueueFailed(batch.id, queueId, 'Generation failed');
          this.stopQueuePolling(queueId);
        }

        this.saveToStorage();
        this.notifyListeners();
        
      } catch (error) {
        console.error(`[BackgroundQueueManager] Error polling queue ${queueId}:`, error);
        
        // Don't fail immediately on network errors, retry
        if (attempts > this.MAX_POLL_ATTEMPTS / 2) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          this.markQueueFailed(this.queues.get(queueId)?.batchId || '', queueId, errorMessage);
          this.stopQueuePolling(queueId);
        }
      }
    };

    // Start polling immediately, then at intervals
    poll();
    const interval = setInterval(poll, this.POLL_INTERVAL);
    this.pollingIntervals.set(queueId, interval);
  }

  // Stop polling for a specific queue
  private stopQueuePolling(queueId: string): void {
    const interval = this.pollingIntervals.get(queueId);
    if (interval) {
      console.log(`[BackgroundQueueManager] Stopping polling for queue ${queueId}`);
      clearInterval(interval);
      this.pollingIntervals.delete(queueId);
    }
  }

  // Force restart all polling - aggressive cleanup
  forceRestartPolling() {
    console.log('[BackgroundQueueManager] Force restarting all polling');

    // Clear all existing intervals
    this.pollingIntervals.forEach((interval, queueId) => {
      clearInterval(interval);
      console.log(`[BackgroundQueueManager] Cleared interval for queue ${queueId}`);
    });
    this.pollingIntervals.clear();

    // Clear global polling
    if (this.globalPollingInterval) {
      clearInterval(this.globalPollingInterval);
      this.globalPollingInterval = null;
    }

    // Restart global polling after a short delay
    setTimeout(() => {
      this.startGlobalPolling();
    }, 1000);
  }

  // Mark a queue as failed with custom error
  markQueueFailed(batchId: string, queueId: string, error: string) {
    console.log(`[BackgroundQueueManager] Marking queue ${queueId} as failed: ${error}`);

    const queue = this.queues.get(queueId);
    const batch = this.batches.get(batchId);

    if (!queue || !batch) {
      console.error(`[BackgroundQueueManager] Queue ${queueId} or batch ${batchId} not found`);
      return;
    }

    // Update queue status
    queue.status = 'failed';
    queue.error = error;
    queue.completedAt = new Date();

    // Update batch counters
    batch.failedCount++;

    // Update batch status
    this.updateBatchStatus(batch);

    // Stop polling for this queue
    this.stopQueuePolling(queueId);

    // Save and notify
    this.saveToStorage();
    this.notifyListeners();
  }

  // Check if batch is complete and update status accordingly
  private checkBatchCompletion(batchId: string) {
    const batch = this.batches.get(batchId);
    if (!batch) return;

    this.updateBatchStatus(batch);
  }

  // Update batch status based on queue statuses
  updateBatchStatus(batch: GenerationBatch): void {
    const totalQueues = batch.queues.length;
    const completedQueues = batch.queues.filter(q => q.status === 'completed').length;
    const failedQueues = batch.queues.filter(q => q.status === 'failed').length;
    const finishedQueues = completedQueues + failedQueues;

    batch.completedCount = completedQueues;
    batch.failedCount = failedQueues;

    if (finishedQueues === totalQueues) {
      if (completedQueues === totalQueues) {
        batch.status = 'completed';
        toast.success(`Batch completed! All ${completedQueues} images generated successfully.`);
      } else if (completedQueues > 0) {
        batch.status = 'partial';
        toast.success(`Batch completed! ${completedQueues} of ${totalQueues} images generated successfully.`);
      } else {
        batch.status = 'failed';
        toast.error(`Batch failed! No images were generated successfully.`);
      }
    }
  }

  // Start global polling for recovery
  private startGlobalPolling(): void {
    if (this.isPolling) return;

    this.isPolling = true;
    console.log('[BackgroundQueueManager] Starting global polling');

    // Resume polling for any active queues
    for (const [queueId, queue] of this.queues) {
      if (queue.status === 'generating' || queue.status === 'pending') {
        console.log(`[BackgroundQueueManager] Resuming polling for queue ${queueId}`);
        this.startQueuePolling(queueId);
      }
    }
  }

  // Stop global polling
  private stopGlobalPolling(): void {
    console.log('[BackgroundQueueManager] Stopping global polling');
    this.isPolling = false;

    // Stop all individual queue polling
    for (const queueId of this.pollingIntervals.keys()) {
      this.stopQueuePolling(queueId);
    }
  }

  // Debug methods for troubleshooting
  getDebugInfo() {
    return {
      totalBatches: this.batches.size,
      totalQueues: this.queues.size,
      pollingIntervals: this.pollingIntervals.size,
      isPolling: this.isPolling,
      activeBatches: Array.from(this.batches.values()).filter(b =>
        b.status === 'pending' || b.status === 'generating'
      ).length,
      activeQueues: Array.from(this.queues.values()).filter(q =>
        q.status === 'generating' || q.status === 'pending'
      ).length,
    };
  }

  // Force restart polling (for debugging)
  forceRestartPolling(): void {
    console.log('[BackgroundQueueManager] Force restarting polling');
    this.stopGlobalPolling();
    setTimeout(() => {
      this.startGlobalPolling();
    }, 1000);
  }

  // Save state to localStorage
  saveToStorage(): void {
    try {
      const data = {
        batches: Array.from(this.batches.entries()),
        queues: Array.from(this.queues.entries()),
        timestamp: Date.now(),
      };
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(data));
    } catch (error) {
      console.error('[BackgroundQueueManager] Failed to save to storage:', error);
    }
  }

  // Load state from localStorage
  private loadFromStorage(): void {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      if (!stored) return;

      const data = JSON.parse(stored);
      
      // Only load recent batches (last 24 hours)
      const dayAgo = Date.now() - 24 * 60 * 60 * 1000;
      
      for (const [batchId, batch] of data.batches) {
        const batchDate = new Date(batch.createdAt).getTime();
        if (batchDate > dayAgo) {
          // Convert date strings back to Date objects
          batch.createdAt = new Date(batch.createdAt);
          this.batches.set(batchId, batch);
        }
      }

      for (const [queueId, queue] of data.queues) {
        const batch = this.batches.get(queue.batchId);
        if (batch) {
          // Convert date strings back to Date objects
          queue.createdAt = new Date(queue.createdAt);
          if (queue.completedAt) {
            queue.completedAt = new Date(queue.completedAt);
          }
          this.queues.set(queueId, queue);
        }
      }

      console.log(`[BackgroundQueueManager] Loaded ${this.batches.size} batches and ${this.queues.size} queues from storage`);
    } catch (error) {
      console.error('[BackgroundQueueManager] Failed to load from storage:', error);
    }
  }

  // Public API methods
  getBatches(): GenerationBatch[] {
    return Array.from(this.batches.values()).sort((a, b) => 
      new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    );
  }

  getBatch(batchId: string): GenerationBatch | undefined {
    return this.batches.get(batchId);
  }

  getActiveBatches(): GenerationBatch[] {
    return this.getBatches().filter(batch => 
      batch.status === 'pending' || batch.status === 'generating'
    );
  }

  getBatchProgress(batchId: string): BatchProgress | null {
    const batch = this.batches.get(batchId);
    if (!batch) return null;

    const totalProgress = batch.queues.reduce((sum, queue) => sum + queue.progress, 0);
    const overallProgress = batch.queues.length > 0 ? totalProgress / batch.queues.length : 0;

    return {
      batchId,
      overallProgress,
      status: batch.status,
      completedCount: batch.completedCount,
      failedCount: batch.failedCount,
      totalCount: batch.totalCount,
    };
  }

  // Subscribe to batch updates
  subscribe(listener: (batches: GenerationBatch[]) => void): () => void {
    this.listeners.add(listener);
    return () => this.listeners.delete(listener);
  }

  notifyListeners(): void {
    const batches = this.getBatches();
    this.listeners.forEach(listener => listener(batches));
  }

  // Clean up old batches
  cleanup(): void {
    const weekAgo = Date.now() - 7 * 24 * 60 * 60 * 1000;
    
    for (const [batchId, batch] of this.batches) {
      const batchDate = new Date(batch.createdAt).getTime();
      if (batchDate < weekAgo && (batch.status === 'completed' || batch.status === 'failed')) {
        this.batches.delete(batchId);
        
        // Remove associated queues
        for (const queue of batch.queues) {
          this.queues.delete(queue.queueId);
          this.stopQueuePolling(queue.queueId);
        }
      }
    }
    
    this.saveToStorage();
    this.notifyListeners();
  }
}

export const backgroundQueueManager = BackgroundQueueManager.getInstance();
