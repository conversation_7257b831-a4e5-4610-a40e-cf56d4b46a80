import React, { createContext, useContext, useEffect, useState, useCallback } from 'react';
import { backgroundQueueManager, GenerationBatch, BatchProgress } from '../services/backgroundQueueManager';
import { FashionLabImageService } from '../services/fashionLabImageService';
import { toast } from 'react-hot-toast';
import { supabase } from '../components/common/utils/supabase';

interface BackgroundQueueContextType {
  // Batch management
  batches: GenerationBatch[];
  activeBatches: GenerationBatch[];
  createBatch: (params: CreateBatchParams) => Promise<string>;
  getBatch: (batchId: string) => GenerationBatch | undefined;
  getBatchProgress: (batchId: string) => BatchProgress | null;
  
  // Queue management
  addQueueToBatch: (batchId: string, queueId: string, imageIndex: number) => void;
  markQueueFailed: (batchId: string, queueId: string, error: string) => void;
  
  // Utility
  cleanup: () => void;
  isGenerating: boolean;
}

interface CreateBatchParams {
  collectionId: string;
  prompt: string;
  metadata?: Record<string, unknown>;
  numImages: number;
  apiDelay: number;
  seeds?: number[];
  faceImage: string;
  image2: string;
  image3: string;
  image4: string;
}

const BackgroundQueueContext = createContext<BackgroundQueueContextType | undefined>(undefined);

export function BackgroundQueueProvider({ children }: { children: React.ReactNode }) {
  const [batches, setBatches] = useState<GenerationBatch[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);

  // Subscribe to batch updates
  useEffect(() => {
    const unsubscribe = backgroundQueueManager.subscribe((updatedBatches) => {
      setBatches(updatedBatches);
      
      // Update isGenerating based on active batches
      const hasActiveBatches = updatedBatches.some(batch => 
        batch.status === 'pending' || batch.status === 'generating'
      );
      setIsGenerating(hasActiveBatches);
    });

    // Initial load
    setBatches(backgroundQueueManager.getBatches());
    
    return unsubscribe;
  }, []);

  // Create a new batch and start generation
  const createBatch = useCallback(async (params: CreateBatchParams): Promise<string> => {
    try {
      // Create the batch
      const batchId = backgroundQueueManager.createBatch({
        collectionId: params.collectionId,
        prompt: params.prompt,
        metadata: params.metadata,
        numImages: params.numImages,
        apiDelay: params.apiDelay,
        seeds: params.seeds,
      });

      console.log(`[BackgroundQueue] Starting generation for batch ${batchId} with ${params.numImages} images`);

      // Get user and organization info
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('User not authenticated');
      }

      // Get organization_id from collection
      const { data: collection, error: collectionError } = await supabase
        .from('collections')
        .select('organization_id')
        .eq('id', params.collectionId)
        .single();

      if (collectionError || !collection) {
        console.error('Failed to get collection organization:', collectionError);
        throw new Error('Failed to get collection organization');
      }

      // Create batch record in database
      const { data: batchRecord, error: batchError } = await supabase
        .from('ai_generation_batches')
        .insert({
          batch_id: batchId,
          collection_id: params.collectionId,
          organization_id: collection.organization_id,
          user_id: user.id,
          prompt: params.prompt,
          status: 'pending',
          total_images: params.numImages,
          metadata: params.metadata || {},
          settings: {
            seeds: params.seeds,
            apiDelay: params.apiDelay,
            modelName: params.metadata?.modelName,
            angle: params.metadata?.angle,
          },
        })
        .select()
        .single();

      if (batchError) {
        console.error('Failed to create batch record:', batchError);
        // Continue anyway - batch will work without DB record
      } else {
        console.log('Created batch record in database:', batchRecord);
      }

      // Start generating images for this batch
      const queueIds: string[] = [];
      const errors: string[] = [];

      for (let i = 0; i < params.numImages; i++) {
        try {
          console.log(`[BackgroundQueue] Generating image ${i + 1}/${params.numImages} for batch ${batchId}`);

          // Add configurable delay between API calls
          if (i > 0 && params.apiDelay > 0) {
            await new Promise(resolve => setTimeout(resolve, params.apiDelay));
          }
          
          const result = await FashionLabImageService.generateImages({
            prompt: params.prompt,
            faceImage: params.faceImage,
            image2: params.image2,
            image3: params.image3,
            image4: params.image4,
            collectionId: params.collectionId,
            storeOnCompletion: true,
            metadata: {
              ...params.metadata,
              batchId,
              imageIndex: i + 1,
              totalImages: params.numImages,
            },
            // Include seeds if provided
            ...(params.seeds && params.seeds[0] !== null && params.seeds[0] !== undefined && { seed1: params.seeds[0] }),
            ...(params.seeds && params.seeds[1] !== null && params.seeds[1] !== undefined && { seed2: params.seeds[1] }),
            ...(params.seeds && params.seeds[2] !== null && params.seeds[2] !== undefined && { seed3: params.seeds[2] }),
            ...(params.seeds && params.seeds[3] !== null && params.seeds[3] !== undefined && { seed4: params.seeds[3] }),
            numImages: 1, // Always 1 since we're making individual calls
          });

          if (result && result.queue_id) {
            queueIds.push(result.queue_id);
            console.log(`[BackgroundQueue] Successfully queued image ${i + 1}/${params.numImages} with queue_id: ${result.queue_id}`);

            // Add queue to batch - this will start background polling
            backgroundQueueManager.addQueueToBatch(batchId, result.queue_id, i + 1);
          } else {
            console.error(`[BackgroundQueue] Failed to get queue_id for image ${i + 1}/${params.numImages}`);
            errors.push(`Image ${i + 1}: No queue_id received`);
            backgroundQueueManager.markQueueFailed(batchId, `failed_${i}`, 'No queue_id received');
          }
        } catch (error) {
          console.error(`[BackgroundQueue] Error generating image ${i + 1}/${params.numImages}:`, error);
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          errors.push(`Image ${i + 1}: ${errorMessage}`);
          backgroundQueueManager.markQueueFailed(batchId, `failed_${i}`, errorMessage);
          
          // Continue with remaining images instead of failing completely
          continue;
        }
      }

      // Show initial feedback
      if (queueIds.length > 0) {
        if (errors.length > 0) {
          toast.success(`Started generating ${queueIds.length} of ${params.numImages} images. Some failed to start.`);
        } else {
          toast.success(`Started generating ${queueIds.length} images. Generation will continue in background.`);
        }
      } else {
        toast.error(`Failed to start any image generation. Errors: ${errors.join('; ')}`);
      }

      return batchId;
    } catch (error) {
      console.error('[BackgroundQueue] Error creating batch:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      toast.error(`Failed to start image generation: ${errorMessage}`);
      throw error;
    }
  }, []);

  // Get active batches
  const activeBatches = batches.filter(batch => 
    batch.status === 'pending' || batch.status === 'generating'
  );

  // Context value
  const value: BackgroundQueueContextType = {
    batches,
    activeBatches,
    createBatch,
    getBatch: (batchId: string) => backgroundQueueManager.getBatch(batchId),
    getBatchProgress: (batchId: string) => backgroundQueueManager.getBatchProgress(batchId),
    addQueueToBatch: (batchId: string, queueId: string, imageIndex: number) => 
      backgroundQueueManager.addQueueToBatch(batchId, queueId, imageIndex),
    markQueueFailed: (batchId: string, queueId: string, error: string) => 
      backgroundQueueManager.markQueueFailed(batchId, queueId, error),
    cleanup: () => backgroundQueueManager.cleanup(),
    isGenerating,
  };

  return (
    <BackgroundQueueContext.Provider value={value}>
      {children}
    </BackgroundQueueContext.Provider>
  );
}

export function useBackgroundQueue(): BackgroundQueueContextType {
  const context = useContext(BackgroundQueueContext);
  if (context === undefined) {
    throw new Error('useBackgroundQueue must be used within a BackgroundQueueProvider');
  }
  return context;
}

// Hook for getting batch-specific data
export function useBatch(batchId: string | null) {
  const { getBatch, getBatchProgress } = useBackgroundQueue();
  const [batch, setBatch] = useState<GenerationBatch | undefined>(undefined);
  const [progress, setProgress] = useState<BatchProgress | null>(null);

  useEffect(() => {
    if (!batchId) {
      setBatch(undefined);
      setProgress(null);
      return;
    }

    const updateBatch = () => {
      setBatch(getBatch(batchId));
      setProgress(getBatchProgress(batchId));
    };

    // Initial update
    updateBatch();

    // Subscribe to updates
    const unsubscribe = backgroundQueueManager.subscribe(() => {
      updateBatch();
    });

    return unsubscribe;
  }, [batchId, getBatch, getBatchProgress]);

  return { batch, progress };
}

// Hook for monitoring active generations
export function useActiveGenerations() {
  const { activeBatches } = useBackgroundQueue();
  
  return {
    activeBatches,
    hasActiveGenerations: activeBatches.length > 0,
    totalActiveImages: activeBatches.reduce((sum, batch) => sum + batch.totalCount, 0),
    totalCompletedImages: activeBatches.reduce((sum, batch) => sum + batch.completedCount, 0),
  };
}
