-- Create table to track AI generation batches
CREATE TABLE IF NOT EXISTS public.ai_generation_batches (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  batch_id TEXT NOT NULL UNIQUE, -- The batch_xxx identifier from frontend
  collection_id UUID NOT NULL REFERENCES public.collections(id) ON DELETE CASCADE,
  organization_id UUID NOT NULL REFERENCES public.organizations(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  prompt TEXT NOT NULL,
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'generating', 'completed', 'failed', 'partial')),
  total_images INTEGER NOT NULL DEFAULT 0,
  completed_images INTEGER DEFAULT 0,
  failed_images INTEGER DEFAULT 0,
  metadata JSONB DEFAULT '{}',
  settings JSONB DEFAULT '{}', -- Store generation settings (seeds, model, angle, etc.)
  created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
  completed_at TIMESTAMPTZ
);

-- Create indexes for performance
CREATE INDEX idx_ai_generation_batches_batch_id ON public.ai_generation_batches(batch_id);
CREATE INDEX idx_ai_generation_batches_collection_id ON public.ai_generation_batches(collection_id);
CREATE INDEX idx_ai_generation_batches_organization_id ON public.ai_generation_batches(organization_id);
CREATE INDEX idx_ai_generation_batches_user_id ON public.ai_generation_batches(user_id);
CREATE INDEX idx_ai_generation_batches_status ON public.ai_generation_batches(status);
CREATE INDEX idx_ai_generation_batches_created_at ON public.ai_generation_batches(created_at DESC);

-- Add batch_id foreign key to ai_generated_images table
ALTER TABLE public.ai_generated_images 
ADD COLUMN IF NOT EXISTS batch_table_id UUID REFERENCES public.ai_generation_batches(id) ON DELETE SET NULL;

-- Create index for the new foreign key
CREATE INDEX IF NOT EXISTS idx_ai_generated_images_batch_table_id 
ON public.ai_generated_images(batch_table_id);

-- Enable RLS on the batches table
ALTER TABLE public.ai_generation_batches ENABLE ROW LEVEL SECURITY;

-- RLS policies for ai_generation_batches table

-- Users can view batches from their organization
CREATE POLICY "Users can view their organization's AI generation batches"
ON public.ai_generation_batches FOR SELECT
USING (
  -- Platform admins can view all batches
  EXISTS (
    SELECT 1
    FROM public.users u
    WHERE u.id = auth.uid()
    AND u.role IN ('platform_super', 'platform_admin')
  )
  OR
  -- Organization members can view their organization's batches
  organization_id IN (
    SELECT organization_id
    FROM public.organization_memberships
    WHERE user_id = auth.uid()
  )
);

-- Users can create batches for their organization
CREATE POLICY "Users can create AI generation batches for their organization"
ON public.ai_generation_batches FOR INSERT
WITH CHECK (
  user_id = auth.uid() AND
  (
    -- Platform admins can create batches for any organization
    EXISTS (
      SELECT 1
      FROM public.users u
      WHERE u.id = auth.uid()
      AND u.role IN ('platform_super', 'platform_admin')
    )
    OR
    -- Regular users must be members of the organization
    EXISTS (
      SELECT 1
      FROM public.collections c
      JOIN public.organization_memberships om ON om.organization_id = c.organization_id
      WHERE c.id = collection_id
      AND om.user_id = auth.uid()
    )
  )
);

-- Users can update their own batches
CREATE POLICY "Users can update their own AI generation batches"
ON public.ai_generation_batches FOR UPDATE
USING (
  user_id = auth.uid()
  OR
  -- Platform admins can update any batch
  EXISTS (
    SELECT 1
    FROM public.users u
    WHERE u.id = auth.uid()
    AND u.role IN ('platform_super', 'platform_admin')
  )
)
WITH CHECK (
  user_id = auth.uid()
  OR
  -- Platform admins can update any batch
  EXISTS (
    SELECT 1
    FROM public.users u
    WHERE u.id = auth.uid()
    AND u.role IN ('platform_super', 'platform_admin')
  )
);

-- Users can delete their own batches
CREATE POLICY "Users can delete their own AI generation batches"
ON public.ai_generation_batches FOR DELETE
USING (
  user_id = auth.uid()
  OR
  -- Platform admins can delete any batch
  EXISTS (
    SELECT 1
    FROM public.users u
    WHERE u.id = auth.uid()
    AND u.role IN ('platform_super', 'platform_admin')
  )
);

-- Add trigger for updated_at
CREATE TRIGGER handle_ai_generation_batches_updated_at 
BEFORE UPDATE ON public.ai_generation_batches
FOR EACH ROW EXECUTE FUNCTION public.update_modified_column();

-- Function to update batch statistics
CREATE OR REPLACE FUNCTION update_batch_statistics()
RETURNS TRIGGER AS $$
BEGIN
  -- Update batch statistics when an image is added/updated
  IF NEW.batch_table_id IS NOT NULL THEN
    UPDATE public.ai_generation_batches
    SET 
      completed_images = (
        SELECT COUNT(*) 
        FROM public.ai_generated_images 
        WHERE batch_table_id = NEW.batch_table_id 
        AND metadata->>'status' = 'completed'
      ),
      failed_images = (
        SELECT COUNT(*) 
        FROM public.ai_generated_images 
        WHERE batch_table_id = NEW.batch_table_id 
        AND metadata->>'status' = 'failed'
      ),
      updated_at = NOW()
    WHERE id = NEW.batch_table_id;
    
    -- Update batch status if all images are processed
    UPDATE public.ai_generation_batches b
    SET 
      status = CASE
        WHEN b.completed_images = b.total_images THEN 'completed'
        WHEN b.failed_images = b.total_images THEN 'failed'
        WHEN b.completed_images + b.failed_images = b.total_images THEN 'partial'
        WHEN b.completed_images + b.failed_images > 0 THEN 'generating'
        ELSE b.status
      END,
      completed_at = CASE
        WHEN b.completed_images + b.failed_images = b.total_images THEN NOW()
        ELSE b.completed_at
      END
    WHERE id = NEW.batch_table_id;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to update batch statistics
CREATE TRIGGER update_batch_stats_on_image_change
AFTER INSERT OR UPDATE ON public.ai_generated_images
FOR EACH ROW
EXECUTE FUNCTION update_batch_statistics();